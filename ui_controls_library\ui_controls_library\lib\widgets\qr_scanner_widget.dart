import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:file_picker/file_picker.dart';
import '../utils/callback_interpreter.dart';

/// Enum for the different states of the QR scanner widget
enum QrScannerState {
  defaultState,
  hover,
  pressed,
  scanned,
}

/// Extension on Color to provide hex string conversion
extension QrScannerColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

class QrScannerWidget extends StatefulWidget {
  /// Current state of the widget
  final QrScannerState initialState;

  /// Whether to enable drag and drop functionality
  final bool enableDragDrop;

  /// Whether to enable file browsing
  final bool enableFileBrowse;

  /// Whether to enable QR scanning
  final bool enableQrScanning;

  /// Text to display in drag and drop area
  final String dragDropText;

  /// Text to display on browse files button
  final String browseFilesText;

  /// Text to display when camera is active
  final String clickToScanText;

  /// Background color for default state
  final Color defaultBackgroundColor;

  /// Border color for default state
  final Color defaultBorderColor;

  /// Background color for hover state
  final Color hoverBackgroundColor;

  /// Border color for hover state
  final Color hoverBorderColor;

  /// Button color
  final Color buttonColor;

  /// Button text color
  final Color buttonTextColor;

  /// Camera background color
  final Color cameraBackgroundColor;

  /// QR scanner frame color
  final Color scannerFrameColor;

  /// Icon color
  final Color iconColor;

  /// Text color
  final Color textColor;

  /// Border width
  final double borderWidth;

  /// Border radius
  final double borderRadius;

  /// Widget height
  final double height;

  /// Widget width
  final double? width;

  /// Padding inside the widget
  final EdgeInsets padding;

  /// Margin around the widget
  final EdgeInsets margin;

  /// Camera icon size
  final double cameraIconSize;

  /// Upload icon size
  final double uploadIconSize;

  /// Button height
  final double buttonHeight;

  /// Button width
  final double buttonWidth;

  /// Scanner frame size
  final double scannerFrameSize;

  /// Callback when a file is selected
  final Function(File)? onFileSelected;

  /// Callback when a QR code is scanned
  final Function(String)? onQrScanned;

  /// Callback when state changes
  final Function(QrScannerState)? onStateChanged;

  /// Callback when widget is hovered
  final Function(bool)? onHover;

  /// Callback when an error occurs
  final Function(String)? onError;

  /// Whether to show flash/torch button
  final bool showFlashButton;

  /// Whether to auto-close camera after scan
  final bool autoCloseAfterScan;

  /// Accepted file extensions
  final List<String>? allowedExtensions;

  /// Maximum file size in bytes
  final int? maxFileSize;

  /// Whether to vibrate on scan
  final bool vibrateOnScan;

  /// Whether to play sound on scan
  final bool playSoundOnScan;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;

  const QrScannerWidget({
    super.key,
    this.initialState = QrScannerState.defaultState,
    this.enableDragDrop = true,
    this.enableFileBrowse = true,
    this.enableQrScanning = true,
    this.dragDropText = 'Drag & drop',
    this.browseFilesText = 'Browse Files',
    this.clickToScanText = 'Click to scan',
    this.defaultBackgroundColor = Colors.white,
    this.defaultBorderColor = const Color(0xFFE0E0E0),
    this.hoverBackgroundColor = const Color(0xFFf0f5ff),
    this.hoverBorderColor = const Color(0xFF0058FF),
    this.buttonColor = const Color(0xFF0058FF),
    this.buttonTextColor = Colors.white,
    this.cameraBackgroundColor = const Color(0xFF2D2D2D),
    this.scannerFrameColor = Colors.white,
    this.iconColor = const Color(0xFF0058FF),
    this.textColor = const Color(0xFF666666),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.height = 225.0,
    this.width,
    this.padding = const EdgeInsets.all(0.0),
    this.margin = EdgeInsets.zero,
    this.cameraIconSize = 30.0,
    this.uploadIconSize = 20.0,
    this.buttonHeight = 30.0,
    this.buttonWidth = 135.0,
    this.scannerFrameSize = 200.0,
    this.onFileSelected,
    this.onQrScanned,
    this.onStateChanged,
    this.onHover,
    this.onError,
    this.showFlashButton = false,
    this.autoCloseAfterScan = true,
    this.allowedExtensions,
    this.maxFileSize,
    this.vibrateOnScan = false,
    this.playSoundOnScan = false,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
  });

  /// Creates a QrScannerWidget from a JSON map
  factory QrScannerWidget.fromJson(Map<String, dynamic> json) {
    // Parse initial state
    QrScannerState initialState = QrScannerState.defaultState;
    if (json.containsKey('initialState')) {
      final String stateStr = json['initialState'].toString().toLowerCase();
      switch (stateStr) {
        case 'hover':
          initialState = QrScannerState.hover;
          break;
        case 'pressed':
          initialState = QrScannerState.pressed;
          break;
        case 'scanned':
          initialState = QrScannerState.scanned;
          break;
        default:
          initialState = QrScannerState.defaultState;
      }
    }

    // Parse colors
    Color parseColor(dynamic colorValue, Color defaultColor) {
      if (colorValue is String) {
        if (colorValue.startsWith('#')) {
          String hex = colorValue.replaceFirst('#', '');
          if (hex.length == 6) hex = 'FF$hex';
          return Color(int.parse(hex, radix: 16));
        }
      }
      return defaultColor;
    }

    return QrScannerWidget(
      initialState: initialState,
      enableDragDrop: json['enableDragDrop'] as bool? ?? true,
      enableFileBrowse: json['enableFileBrowse'] as bool? ?? true,
      enableQrScanning: json['enableQrScanning'] as bool? ?? true,
      dragDropText: json['dragDropText'] as String? ?? 'Drag & drop',
      browseFilesText: json['browseFilesText'] as String? ?? 'Browse Files',
      clickToScanText: json['clickToScanText'] as String? ?? 'Click to scan',
      defaultBackgroundColor: parseColor(json['defaultBackgroundColor'], Colors.white),
      defaultBorderColor: parseColor(json['defaultBorderColor'], const Color(0xFFE0E0E0)),
      hoverBackgroundColor: parseColor(json['hoverBackgroundColor'], const Color(0xFFf0f5ff)),
      hoverBorderColor: parseColor(json['hoverBorderColor'], const Color(0xFF0058FF)),
      buttonColor: parseColor(json['buttonColor'], const Color(0xFF0058FF)),
      buttonTextColor: parseColor(json['buttonTextColor'], Colors.white),
      cameraBackgroundColor: parseColor(json['cameraBackgroundColor'], const Color(0xFF2D2D2D)),
      scannerFrameColor: parseColor(json['scannerFrameColor'], Colors.white),
      iconColor: parseColor(json['iconColor'], const Color(0xFF0058FF)),
      textColor: parseColor(json['textColor'], const Color(0xFF666666)),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      height: (json['height'] as num?)?.toDouble() ?? 200.0,
      width: (json['width'] as num?)?.toDouble(),
      cameraIconSize: (json['cameraIconSize'] as num?)?.toDouble() ?? 30.0,
      uploadIconSize: (json['uploadIconSize'] as num?)?.toDouble() ?? 20.0,
      buttonHeight: (json['buttonHeight'] as num?)?.toDouble() ?? 40.0,
      buttonWidth: (json['buttonWidth'] as num?)?.toDouble() ?? 130.0,
      scannerFrameSize: (json['scannerFrameSize'] as num?)?.toDouble() ?? 200.0,
      showFlashButton: json['showFlashButton'] as bool? ?? false,
      autoCloseAfterScan: json['autoCloseAfterScan'] as bool? ?? true,
      vibrateOnScan: json['vibrateOnScan'] as bool? ?? false,
      playSoundOnScan: json['playSoundOnScan'] as bool? ?? false,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      jsonCallbacks: json['callbacks'] as Map<String, dynamic>?,
    );
  }

  @override
  State<QrScannerWidget> createState() => _QrScannerWidgetState();
}

class _QrScannerWidgetState extends State<QrScannerWidget>
    with SingleTickerProviderStateMixin {
  QrScannerState _currentState = QrScannerState.defaultState;
  bool _isHovered = false;
  bool _hasPermission = false;
  MobileScannerController? _controller;
  String? _scannedData;
  Uint8List? _scannedImage;
  bool _isFlashOn = false;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _currentState = widget.initialState;
    _checkPermission();

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  Future<void> _checkPermission() async {
    final status = await Permission.camera.status;
    setState(() {
      _hasPermission = status.isGranted;
    });

    if (!status.isGranted) {
      final result = await Permission.camera.request();
      setState(() {
        _hasPermission = result.isGranted;
      });
    }
  }

  void _changeState(QrScannerState newState) {
    if (_currentState != newState) {
      setState(() {
        _currentState = newState;
      });

      // Call state change callback
      if (widget.onStateChanged != null) {
        widget.onStateChanged!(newState);
      }

      // Execute JSON callback if defined
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onStateChanged')) {
        _executeJsonCallback('onStateChanged', newState.toString());
      }
    }
  }

  void _handleHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    // Handle hover state transitions properly
    if (isHovered && _currentState == QrScannerState.defaultState) {
      _changeState(QrScannerState.hover);
    } else if (!isHovered && _currentState == QrScannerState.hover) {
      _changeState(QrScannerState.defaultState);
    }

    // Call hover callback
    if (widget.onHover != null) {
      widget.onHover!(isHovered);
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onHover')) {
      _executeJsonCallback('onHover', isHovered);
    }
  }

  Future<void> _handleCameraPress() async {
    if (!widget.enableQrScanning) return;

    if (!_hasPermission) {
      await _checkPermission();
      if (!_hasPermission) {
        _handleError('Camera permission is required');
        return;
      }
    }

    _changeState(QrScannerState.pressed);

    // Initialize camera controller
    _controller = MobileScannerController(
      facing: CameraFacing.back,
      torchEnabled: _isFlashOn,
    );
  }

  void _handleCameraClose() {
    _controller?.dispose();
    _controller = null;
    _changeState(QrScannerState.defaultState);
  }

  Future<void> _handleFileBrowse() async {
    if (!widget.enableFileBrowse) return;

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: widget.allowedExtensions ?? ['jpg', 'jpeg', 'png', 'pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        
        // Check file size if specified
        if (widget.maxFileSize != null) {
          final fileSize = await file.length();
          if (fileSize > widget.maxFileSize!) {
            _handleError('File size exceeds maximum allowed size');
            return;
          }
        }

        // Call file selected callback
        if (widget.onFileSelected != null) {
          widget.onFileSelected!(file);
        }

        // Execute JSON callback if defined
        if (widget.useJsonCallbacks &&
            widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onFileSelected')) {
          _executeJsonCallback('onFileSelected', file.path);
        }
      }
    } catch (e) {
      _handleError('Error selecting file: $e');
    }
  }

  void _handleQrScanned(String data, Uint8List? image) {
    setState(() {
      _scannedData = data;
      _scannedImage = image;
    });

    _changeState(QrScannerState.scanned);

    // Call QR scanned callback
    if (widget.onQrScanned != null) {
      widget.onQrScanned!(data);
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onQrScanned')) {
      _executeJsonCallback('onQrScanned', data);
    }

    // Auto-close camera if configured
    if (widget.autoCloseAfterScan) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          _handleCameraClose();
        }
      });
    }
  }

  void _handleError(String error) {
    // Call error callback
    if (widget.onError != null) {
      widget.onError!(error);
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onError')) {
      _executeJsonCallback('onError', error);
    }
  }

  void _toggleFlash() {
    setState(() {
      _isFlashOn = !_isFlashOn;
      _controller?.toggleTorch();
    });
  }

  Widget _buildDottedBorder({
    required Widget child,
    required Color color,
    required double borderRadius,
  }) {
    return CustomPaint(
      painter: DottedBorderPainter(
        color: color,
        borderRadius: borderRadius,
      ),
      child: child,
    );
  }

  Widget _buildDefaultState() {
    final isHovered = _currentState == QrScannerState.hover;
    
    return Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      padding: EdgeInsets.only(top: 10),
      decoration: BoxDecoration(
        color: isHovered ? widget.hoverBackgroundColor : widget.defaultBackgroundColor,
        border: Border.all(
          color: isHovered ? widget.hoverBorderColor : widget.defaultBorderColor,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Camera icon at top
          GestureDetector(
            onTap: _handleCameraPress,
            child: Icon(
              Icons.photo_camera_outlined,
              size: widget.cameraIconSize,
              color: widget.iconColor,
            ),
          ),
          const SizedBox(height: 10),
          
          // Drag and drop area
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildDottedBorder(
                color: isHovered ? widget.hoverBorderColor : widget.defaultBorderColor,
                borderRadius: widget.borderRadius,
                child: Container(
                  width: double.infinity,
                  padding: widget.padding,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.cloud_upload_outlined,
                        size: widget.uploadIconSize,
                        color: widget.iconColor,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        widget.dragDropText,
                        style: TextStyle(
                          color: widget.textColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 10),
          
          // Browse Files button
          SizedBox(
            width: widget.buttonWidth,
            height: widget.buttonHeight,
            child: ElevatedButton(
              onPressed: _handleFileBrowse,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.buttonColor,
                foregroundColor: widget.buttonTextColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                elevation: 0,
              ),
              child: Text(
                widget.browseFilesText,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _buildPressedState() {
    return Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.defaultBackgroundColor,
        border: Border.all(
          color: widget.hoverBorderColor,
          width: widget.borderWidth,
        ),
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Column(
        children: [
          // Camera preview area
          Expanded(
            flex: 2,
            child: Container(
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: widget.cameraBackgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
              ),
              child: Stack(
                children: [
                  // Camera preview
                  if (_controller != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      child: MobileScanner(
                        controller: _controller!,
                        onDetect: (capture) {
                          final List<Barcode> barcodes = capture.barcodes;
                          if (barcodes.isNotEmpty && barcodes[0].rawValue != null) {
                            final String code = barcodes[0].rawValue!;
                            _handleQrScanned(code, capture.image);
                          }
                        },
                        errorBuilder: (context, error, child) {
                          _handleError('Scanner error: $error');
                          return Center(
                            child: Text(
                              'Scanner Error',
                              style: TextStyle(color: widget.scannerFrameColor),
                            ),
                          );
                        },
                      ),
                    ),
                  
                  // Scanner frame overlay
                  Center(
                    child: Container(
                      width: widget.scannerFrameSize,
                      height: widget.scannerFrameSize,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: widget.scannerFrameColor,
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Stack(
                        children: [
                          // Corner frames
                          ...List.generate(4, (index) {
                            return Positioned(
                              top: index < 2 ? 0 : null,
                              bottom: index >= 2 ? 0 : null,
                              left: index % 2 == 0 ? 0 : null,
                              right: index % 2 == 1 ? 0 : null,
                              child: Container(
                                width: 20,
                                height: 20,
                                decoration: BoxDecoration(
                                  border: Border(
                                    top: index < 2 ? BorderSide(color: widget.scannerFrameColor, width: 1) : BorderSide.none,
                                    bottom: index >= 2 ? BorderSide(color: widget.scannerFrameColor, width: 1) : BorderSide.none,
                                    left: index % 2 == 0 ? BorderSide(color: widget.scannerFrameColor, width: 1) : BorderSide.none,
                                    right: index % 2 == 1 ? BorderSide(color: widget.scannerFrameColor, width: 1) : BorderSide.none,
                                  ),
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
                  
                  // Click to scan text
                  Positioned(
                    bottom: 20,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Text(
                        widget.clickToScanText,
                        style: TextStyle(
                          color: widget.scannerFrameColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  
                  // Flash button
                  if (widget.showFlashButton)
                    Positioned(
                      top: 20,
                      right: 20,
                      child: IconButton(
                        onPressed: _toggleFlash,
                        icon: Icon(
                          _isFlashOn ? Icons.flash_on : Icons.flash_off,
                          color: widget.scannerFrameColor,
                        ),
                      ),
                    ),
                  
                  // Close button
                  Positioned(
                    top: 20,
                    left: 20,
                    child: IconButton(
                      onPressed: _handleCameraClose,
                      icon: Icon(
                        Icons.close,
                        color: widget.scannerFrameColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Drag and drop area (smaller)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              border: Border.all(
                color: widget.hoverBorderColor,
                width: 1.0,
              ),
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.cloud_upload_outlined,
                  size: 20,
                  color: widget.iconColor,
                ),
                const SizedBox(width: 10),
                Text(
                  widget.dragDropText,
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 10),
          
          // Browse Files button
          SizedBox(
            width: widget.buttonWidth,
            height: widget.buttonHeight,
            child: ElevatedButton(
              onPressed: _handleFileBrowse,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.buttonColor,
                foregroundColor: widget.buttonTextColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                elevation: 0,
              ),
              child: Text(
                widget.browseFilesText,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildScannedState() {
    return Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.defaultBackgroundColor,
        border: Border.all(
          color: widget.hoverBorderColor,
          width: widget.borderWidth,
        ),
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Column(
        children: [
          // Scanned QR code display area
          Expanded(
            flex: 2,
            child: Container(
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(widget.borderRadius),
              ),
              child: Stack(
                children: [
                  // QR code image or placeholder
                  Center(
                    child: _scannedImage != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(widget.borderRadius),
                            child: Image.memory(
                              _scannedImage!,
                              fit: BoxFit.contain,
                            ),
                          )
                        : Container(
                            width: 150,
                            height: 150,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade300,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.qr_code,
                              size: 80,
                              color: Colors.grey,
                            ),
                          ),
                  ),
                  
                  // Click to scan overlay
                  Positioned(
                    bottom: 20,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          widget.clickToScanText,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  // Close button
                  Positioned(
                    top: 10,
                    right: 10,
                    child: IconButton(
                      onPressed: () => _changeState(QrScannerState.defaultState),
                      icon: const Icon(
                        Icons.close,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Drag and drop area (smaller)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              border: Border.all(
                color: widget.hoverBorderColor,
                width: 1.0,
              ),
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.cloud_upload_outlined,
                  size: 20,
                  color: widget.iconColor,
                ),
                const SizedBox(width: 10),
                Text(
                  widget.dragDropText,
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 10),
          
          // Browse Files button
          SizedBox(
            width: widget.buttonWidth,
            height: widget.buttonHeight,
            child: ElevatedButton(
              onPressed: _handleFileBrowse,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.buttonColor,
                foregroundColor: widget.buttonTextColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                elevation: 0,
              ),
              child: Text(
                widget.browseFilesText,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    switch (_currentState) {
      case QrScannerState.defaultState:
      case QrScannerState.hover:
        content = _buildDefaultState();
        break;
      case QrScannerState.pressed:
        content = _buildPressedState();
        break;
      case QrScannerState.scanned:
        content = _buildScannedState();
        break;
    }

    // Add hover detection
    return MouseRegion(
      onEnter: (_) => _handleHover(true),
      onExit: (_) => _handleHover(false),
      child: content,
    );
  }
}

/// Custom painter for dotted border
class DottedBorderPainter extends CustomPainter {
  final Color color;
  final double borderRadius;

  DottedBorderPainter({
    required this.color,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    const double dashWidth = 5.0;
    const double dashSpace = 5.0;

    final RRect rrect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Radius.circular(borderRadius),
    );

    final Path path = Path()..addRRect(rrect);
    
    _drawDashedPath(canvas, path, paint, dashWidth, dashSpace);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint, double dashWidth, double dashSpace) {
    // Simple dotted border implementation
    final Rect rect = Rect.fromLTWH(0, 0, path.getBounds().width, path.getBounds().height);
    
    // Draw top border
    _drawDashedLine(canvas, paint, Offset(rect.left, rect.top), Offset(rect.right, rect.top), dashWidth, dashSpace);
    
    // Draw right border
    _drawDashedLine(canvas, paint, Offset(rect.right, rect.top), Offset(rect.right, rect.bottom), dashWidth, dashSpace);
    
    // Draw bottom border
    _drawDashedLine(canvas, paint, Offset(rect.right, rect.bottom), Offset(rect.left, rect.bottom), dashWidth, dashSpace);
    
    // Draw left border
    _drawDashedLine(canvas, paint, Offset(rect.left, rect.bottom), Offset(rect.left, rect.top), dashWidth, dashSpace);
  }

  void _drawDashedLine(Canvas canvas, Paint paint, Offset start, Offset end, double dashWidth, double dashSpace) {
    final double totalDistance = (end - start).distance;
    final Offset direction = (end - start) / totalDistance;
    
    double currentDistance = 0.0;
    while (currentDistance < totalDistance) {
      final Offset dashStart = start + direction * currentDistance;
      final double remainingDistance = totalDistance - currentDistance;
      final double currentDashWidth = dashWidth > remainingDistance ? remainingDistance : dashWidth;
      final Offset dashEnd = dashStart + direction * currentDashWidth;
      
      canvas.drawLine(dashStart, dashEnd, paint);
      currentDistance += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(DottedBorderPainter oldDelegate) {
    return oldDelegate.color != color || oldDelegate.borderRadius != borderRadius;
  }
}
