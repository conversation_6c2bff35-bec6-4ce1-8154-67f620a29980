import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_create_button.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class Project {
  final String projectName;
  final String createdOn;
  final String lastModified;
  final String lastModifiedBy;
  final String status;

  Project({
    required this.projectName,
    required this.createdOn,
    required this.lastModified,
    required this.lastModifiedBy,
    required this.status,
  });

  factory Project.fromJson(Map<String, dynamic> json) {
    return Project(
      projectName: json['projectName'] as String,
      createdOn: json['createdOn'] as String,
      lastModified: json['lastModified'] as String,
      lastModifiedBy: json['lastModifiedBy'] as String,
      status: json['status'] as String,
    );
  }
}

// ignore: must_be_immutable
class WebMyProjectsScreen extends StatefulWidget {
  WebMyProjectsScreen({super.key, this.showNavigationBar = true});

  bool showNavigationBar = true;

  @override
  State<WebMyProjectsScreen> createState() => WebMyProjectsScreenState();
}

class WebMyProjectsScreenState extends State<WebMyProjectsScreen>
    with TickerProviderStateMixin {
  late List<Project> projects;
  List<Project> _allProjects = []; // Store original unfiltered projects
  List<Project> _filteredProjects = []; // Store filtered projects for display
  bool isLoading = true;

  // Search state
  final TextEditingController _searchController = TextEditingController();

  // Pagination state
  int _currentPage = 1;
  int _itemsPerPage = 10;
  int _totalPages = 1;

  // Create project modal state
  bool _showCreateProjectModal = false;
  final TextEditingController _projectNameController = TextEditingController();
  String? _selectedIndustry;
  bool _isDropdownFocused = false;
  
  final List<String> _industries = [
    'E-Commerce',
    'Healthcare',
    'Finance',
    'Education',
    'Technology',
  ];

  // JSON string containing project data
  static const String projectsJsonString = '''
{
  "projects": [
    {
      "projectName": "E Commerce Solution",
      "createdOn": "09/05/2025",
      "lastModified": "10/06/2025",
      "lastModifiedBy": "Debmalaya Mitra",
      "status": "Discovery 50% / Development 0%"
    },
    {
      "projectName": "E Commerce Solution",
      "createdOn": "09/05/2025",
      "lastModified": "10/06/2025",
      "lastModifiedBy": "Debmalaya Mitra",
      "status": "Discovery 50% / Development 0%"
    },
    {
      "projectName": "E Commerce Solution",
      "createdOn": "09/05/2025",
      "lastModified": "10/06/2025",
      "lastModifiedBy": "Debmalaya Mitra",
      "status": "Discovery 50% / Development 0%"
    },
    {
      "projectName": "E Commerce Solution",
      "createdOn": "09/05/2025",
      "lastModified": "10/06/2025",
      "lastModifiedBy": "Debmalaya Mitra",
      "status": "Discovery 50% / Development 0%"
    },
    {
      "projectName": "Fashion & Apparel Platform",
      "createdOn": "08/05/2025",
      "lastModified": "09/06/2025",
      "lastModifiedBy": "John Smith",
      "status": "Discovery 80% / Development 20%"
    },
    {
      "projectName": "Financial Advisory System",
      "createdOn": "07/05/2025",
      "lastModified": "08/06/2025",
      "lastModifiedBy": "Sarah Johnson",
      "status": "Discovery 100% / Development 45%"
    },
    {
      "projectName": "Home Rentals Platform",
      "createdOn": "06/05/2025",
      "lastModified": "07/06/2025",
      "lastModifiedBy": "Mike Wilson",
      "status": "Discovery 30% / Development 0%"
    },
    {
      "projectName": "Online Grocery Store",
      "createdOn": "05/05/2025",
      "lastModified": "06/06/2025",
      "lastModifiedBy": "Emily Davis",
      "status": "Discovery 90% / Development 60%"
    },
    {
      "projectName": "Courier & Logistics App",
      "createdOn": "04/05/2025",
      "lastModified": "05/06/2025",
      "lastModifiedBy": "David Brown",
      "status": "Discovery 70% / Development 30%"
    },
    {
      "projectName": "Automotive Marketplace",
      "createdOn": "03/05/2025",
      "lastModified": "04/06/2025",
      "lastModifiedBy": "Lisa Anderson",
      "status": "Discovery 60% / Development 15%"
    },
    {
      "projectName": "Fitness & Wellness App",
      "createdOn": "02/05/2025",
      "lastModified": "03/06/2025",
      "lastModifiedBy": "Robert Taylor",
      "status": "Discovery 85% / Development 40%"
    },
    {
      "projectName": "Real Estate Portal",
      "createdOn": "01/05/2025",
      "lastModified": "02/06/2025",
      "lastModifiedBy": "Jennifer White",
      "status": "Discovery 95% / Development 70%"
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _loadProjects();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _loadProjects() {
    try {
      // Parse the JSON string
      final data = json.decode(projectsJsonString);

      // Convert to Project objects
      final List<Project> loadedProjects = (data['projects'] as List)
          .map((projectJson) => Project.fromJson(projectJson))
          .toList();

      setState(() {
        _allProjects = loadedProjects;
        projects = loadedProjects;
        _filteredProjects = loadedProjects;
        isLoading = false;
        _updatePagination();
      });
    } catch (e) {
      setState(() {
        _allProjects = [];
        projects = [];
        _filteredProjects = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading projects: $e');
    }
  }

  // Handle search text changes
  void _onSearchChanged() {
    _filterProjects();
  }

  // Filter projects based on search text
  void _filterProjects() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredProjects = List.from(_allProjects);
      } else {
        _filteredProjects = _allProjects
            .where((project) =>
                project.projectName.toLowerCase().contains(searchText) ||
                project.lastModifiedBy.toLowerCase().contains(searchText) ||
                project.status.toLowerCase().contains(searchText))
            .toList();
      }

      projects = _filteredProjects;
      // Reset to first page when filtering
      _currentPage = 1;
      _updatePagination();
    });
  }

  // Update pagination based on current state
  void _updatePagination() {
    if (projects.isEmpty) {
      _totalPages = 1;
      return;
    }

    _totalPages = (projects.length / _itemsPerPage).ceil();

    // Ensure current page is valid
    if (_currentPage > _totalPages) {
      _currentPage = _totalPages;
    }
    if (_currentPage < 1) {
      _currentPage = 1;
    }
  }

  // Get projects for current page
  List<Project> _getCurrentPageProjects() {
    if (projects.isEmpty) return [];

    int startIndex = (_currentPage - 1) * _itemsPerPage;
    int endIndex = startIndex + _itemsPerPage;

    if (startIndex >= projects.length) return [];
    if (endIndex > projects.length) endIndex = projects.length;

    return projects.sublist(startIndex, endIndex);
  }

  // Navigate to previous page
  void _goToPreviousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
    }
  }

  // Navigate to next page
  void _goToNextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xffF7F9FB) : Colors.transparent,
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              widget.showNavigationBar
                  ?
                  // Top navigation bar
                  Padding(
                      padding: widget.showNavigationBar
                          ? const EdgeInsets.only(
                              left: 94, right: 94, bottom: 0.0, top: 0)
                          : EdgeInsets.zero,
                      child: Column(
                        children: [
                          const HoverNavItems(),
                          SizedBox(height: 20),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Project text
                              Expanded(
                                child: Text(
                                  'Project',
                                  style: FontManager.getCustomStyle(
                                    fontSize: FontManager.s14,
                                    fontWeight: FontWeight.w600,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              ),

                              // Search bar with filter
                              Expanded(
                                child: Container(
                                  width: MediaQuery.of(context).size.width / 3.722,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(
                                        color: Colors.grey.shade200),
                                  ),
                                  child: Row(
                                    children: [
                                      // Search text field
                                      Expanded(
                                        child: Padding(
                                          padding: const EdgeInsets.only(left: 16.0),
                                          child: TextField(
                                            controller: _searchController,
                                            cursorHeight: 16,
                                            decoration: InputDecoration(
                                              enabledBorder: InputBorder.none,
                                              focusedBorder: InputBorder.none,
                                              hintText: 'Search Projects...',
                                              border: InputBorder.none,
                                              hintStyle: TextStyle(
                                                  fontSize: FontManager.s14,
                                                  color: Color(0xff939393)),
                                              isDense: true,
                                              contentPadding: EdgeInsets.zero,
                                              filled: false,
                                              fillColor: Colors.transparent,
                                            ),
                                          ),
                                        ),
                                      ),
                                      // Search icon
                                      _HoverSvgButton(
                                        normalIconPath: 'assets/images/search.svg',
                                        hoverIconPath: 'assets/images/search.svg',
                                        onPressed: () {
                                          _filterProjects();
                                        },
                                        imageWidth: 20,
                                        imageHeight: 20,
                                        showBorderOnHover: false,
                                      ),
                                      // Divider between search and filter
                                      Container(
                                        height: 23,
                                        width: 1,
                                        color: Colors.grey.shade200,
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 4),
                                      ),
                                      // Filter icon
                                      _HoverSvgButton(
                                        normalIconPath:
                                            'assets/images/filter-icon.svg',
                                        hoverIconPath:
                                            'assets/images/filter-hover.svg',
                                        onPressed: () {},
                                        imageWidth: 32,
                                        imageHeight: 32,
                                        showBorderOnHover: false,
                                      ),
                                      const SizedBox(width: 8),
                                    ],
                                  ),
                                ),
                              ),

                              // Create Project button
                              Expanded(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    HoverCreateButton(
                                      text: 'Create Project',
                                      onPressed: () {
                                        setState(() {
                                          _showCreateProjectModal = true;
                                        });
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                        ],
                      ),
                    )
                  : Container(),
              // Main content
              myProjects(context),
            ],
          ),
          // Create Project Modal
          if (_showCreateProjectModal) _buildCreateProjectModal(),
        ],
      ),
    );
  }

  Widget myProjects(context) {
    return Expanded(
      child: SingleChildScrollView(
        physics: widget.showNavigationBar
            ? const AlwaysScrollableScrollPhysics()
            : const NeverScrollableScrollPhysics(),
        child: Container(
          padding:
              const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 32),
              // Projects table
              isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : projects.isEmpty
                      ? const Center(child: Text('No projects found'))
                      : _buildProjectsTable(),
              // Pagination controls
              if (projects.isNotEmpty && _totalPages > 1)
                Container(
                  margin: const EdgeInsets.only(top: 20, bottom: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Page info
                      Text(
                        'Page $_currentPage of $_totalPages',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s14,
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey,
                        ),
                      ),
                      // Navigation buttons
                      Row(
                        children: [
                          // Previous button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_left, size: 20),
                            onPressed:
                                _currentPage > 1 ? _goToPreviousPage : null,
                          ),
                          const SizedBox(width: 8),
                          // Next button
                          _HoverPaginationButton(
                            icon: const Icon(Icons.chevron_right, size: 20),
                            onPressed: _currentPage < _totalPages
                                ? _goToNextPage
                                : null,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProjectsTable() {
    List<Project> currentPageProjects = _getCurrentPageProjects();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        // border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Color(0xFFF7F9FB),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'Project Name',
                       style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Created on',
                       style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Last Modified',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Last Modified by',
                       style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Status',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                  ),
                ),
              ],
            ),
          ),
          // Table rows
          ...currentPageProjects.asMap().entries.map((entry) {
            int index = entry.key;
            Project project = entry.value;
            bool isEvenRow = index % 2 == 0;

            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
             color: isEvenRow ? Colors.white : Color(0xFFF7F9FB),
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey.shade200,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Text(
                      project.projectName,
                        style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily:FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      project.createdOn,
                         style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      project.lastModified,
                        style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      project.lastModifiedBy,
                     style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      project.status,
                        style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildCreateProjectModal() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showCreateProjectModal = false;
        });
      },
      child: Container(
        color: Colors.black.withOpacity(0.5),
        child: Center(
          child: GestureDetector(
            onTap: () {}, // Prevent closing when tapping on the modal content
            child: Container(
              width: 400,
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      'Create A Project',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s24,
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Name field
                  Text(
                    'Name',
                       style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 40,
                    child: TextField(
                      controller: _projectNameController,
                      decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide(color: Color(0xff0058FF), width: 1),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                        hintText: 'Enter project name',
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: FontManager.s14,
                          fontFamily:FontManager.fontFamilyTiemposText,
                        ),
                      ),
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  
                  // Industry field
                  Text(
                    'Industry',
                     style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black87,
                      ),
                  ),
                  const SizedBox(height: 8),
                  Focus(
                    onFocusChange: (hasFocus) {
                      setState(() {
                        _isDropdownFocused = hasFocus;
                      });
                    },
                    child: Container(
                      width: double.infinity,
                      height: 40,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _isDropdownFocused 
                              ? Color(0xff0058FF) 
                              : Colors.grey.shade300,
                          width: _isDropdownFocused ? 1 : 1,
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _selectedIndustry,
                          hint: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Text(
                              'Select industry',
                              style: FontManager.getCustomStyle(
                                fontSize: FontManager.s14,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey.shade500,
                              ),
                            ),
                          ),
                          icon: Padding(
                            padding: const EdgeInsets.only(right: 12),
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          isExpanded: true,
                          items: _industries.map((String industry) {
                            return DropdownMenuItem<String>(
                              value: industry,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 12),
                                child: Text(
                                  industry,
                                  style: FontManager.getCustomStyle(
                                    fontSize: FontManager.s14,
                                    fontFamily: FontManager.fontFamilyTiemposText,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            setState(() {
                              _selectedIndustry = newValue;
                            });
                          },
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),
                  
                  // Start button
                  Center(
                    child: SizedBox(
                      width: 200,
                      height: 44,
                      child: ElevatedButton(
                      onPressed: () {
                        // Handle project creation
                        if (_projectNameController.text.isNotEmpty && _selectedIndustry != null) {
                          // Create new project object
                          final newProject = Project(
                            projectName: _projectNameController.text,
                            createdOn: DateTime.now().toString().substring(0, 10).replaceAll('-', '/'),
                            lastModified: DateTime.now().toString().substring(0, 10).replaceAll('-', '/'),
                            lastModifiedBy: 'Current User', 
                            status: 'Discovery 0% / Development 0%',
                          );
                          
                          // Store project name for success message
                          final projectName = _projectNameController.text;
                          
                          // Add new project to the beginning of the list
                          setState(() {
                            _allProjects.insert(0, newProject);
                            _filteredProjects.insert(0, newProject);
                            projects.insert(0, newProject);
                            _updatePagination();
                            
                            // Close modal and reset form
                            _showCreateProjectModal = false;
                            _projectNameController.clear();
                            _selectedIndustry = null;
                          });
                          
                          // Show success message
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Project "$projectName" created successfully!'),
                              backgroundColor: Colors.green,
                            ),
                          );
                        }
                      },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xff0058FF),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                        child: Text(
                          'Start',
                         style: FontManager.getCustomStyle(
                            fontSize: FontManager.s16,
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _HoverSvgButton extends StatefulWidget {
  final String normalIconPath;
  final String hoverIconPath;
  final VoidCallback onPressed;
  final double imageWidth;
  final double imageHeight;
  final bool showBorderOnHover;

  const _HoverSvgButton({
    required this.normalIconPath,
    required this.hoverIconPath,
    required this.onPressed,
    this.imageWidth = 18,
    this.imageHeight = 18,
    this.showBorderOnHover = true,
  });

  @override
  State<_HoverSvgButton> createState() => _HoverSvgButtonState();
}

class _HoverSvgButtonState extends State<_HoverSvgButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: widget.showBorderOnHover
              ? Border.all(
                  color: isHovered ? Color(0xff0058FF) : Colors.transparent,
                  width: 1.0,
                )
              : null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: SvgPicture.asset(
            isHovered ? widget.hoverIconPath : widget.normalIconPath,
            width: widget.imageWidth,
            height: widget.imageHeight,
          ),
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    bool isDisabled = widget.onPressed == null;

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          border: Border.all(
            color: isDisabled
                ? Colors.grey.shade200
                : isHovered
                    ? Color(0xff0058FF)
                    : Colors.grey.shade300,
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered && !isDisabled ? BorderRadius.zero : null,
          color: isDisabled ? Colors.grey.shade50 : Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: isDisabled
              ? Colors.grey.shade400
              : isHovered
                  ? Color(0xff0058FF)
                  : Colors.black,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
