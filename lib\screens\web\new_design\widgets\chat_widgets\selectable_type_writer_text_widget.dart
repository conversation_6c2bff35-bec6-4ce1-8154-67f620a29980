import 'package:flutter/material.dart';
import 'dart:async';

// class SelectableTypewriterText extends StatefulWidget {
//   final String text;
//   final Duration speed;
//   final TextStyle? style;
//   final VoidCallback? onComplete;

//   const SelectableTypewriterText({
//     required this.text,
//     this.speed = const Duration(milliseconds: 100),
//     this.style,
//     this.onComplete,
//     Key? key,
//   }) : super(key: key);

//   @override
//   _SelectableTypewriterTextState createState() =>
//       _SelectableTypewriterTextState();
// }

// class _SelectableTypewriterTextState extends State<SelectableTypewriterText> {
//   String _displayedText = '';
//   int _index = 0;
//   Timer? _timer;

//   @override
//   void initState() {
//     super.initState();
//     _startTyping();
//   }

//   void _startTyping() {
//     _timer = Timer.periodic(widget.speed, (timer) {
//       if (_index < widget.text.length) {
//         setState(() {
//           _displayedText += widget.text[_index];
//           _index++;
//         });
//         if (widget.text[_index] == '\n') {
//           widget.onComplete?.call();
//         }
//       } else {
//         _timer?.cancel();
//         widget.onComplete?.call();
//       }
//     });
//   }

//   @override
//   void dispose() {
//     _timer?.cancel();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SelectableText(
//       _displayedText,
//       style: widget.style,
//     );
//   }
// }
class SelectableTypewriterText extends StatefulWidget {
  final String text;
  final Duration speed;
  final TextStyle? style;
  final VoidCallback? onComplete;

  const SelectableTypewriterText({
    required this.text,
    this.speed = const Duration(milliseconds: 100),
    this.style,
    this.onComplete,
    Key? key,
  }) : super(key: key);

  @override
  State<SelectableTypewriterText> createState() =>
      _SelectableTypewriterTextState();
}

class _SelectableTypewriterTextState extends State<SelectableTypewriterText> {
  String _displayedText = '';
  int _index = 0;
  Timer? _timer;
  bool _isComplete = false;

  @override
  void initState() {
    super.initState();
    if (!_isComplete) {
      _startTyping();
    } else {
      _displayedText = widget.text;
    }
  }

  void _startTyping() {
    _timer = Timer.periodic(widget.speed, (timer) {
      if (_index < widget.text.length) {
        final nextChar = widget.text[_index];
        setState(() {
          _displayedText += nextChar;
          _index++;
        });

        // Optional: Call onComplete after every newline
        if (nextChar == '\n') {
          widget.onComplete?.call();
        }
      } else {
        _timer?.cancel();
        setState(() {
          _isComplete = true;
        });
        widget.onComplete?.call(); // Final scroll
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SelectableText(
      _isComplete ? widget.text : _displayedText,
      style: widget.style,
    );
  }
}
