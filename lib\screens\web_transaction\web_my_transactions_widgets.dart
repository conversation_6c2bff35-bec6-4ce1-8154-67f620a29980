import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart' as f1;
//import 'package:pie_chart/pie_chart.dart' as pc;
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:nsl/models/WorkflowItem.dart';
import 'package:nsl/theme/app_theme_constants.dart';
import 'package:nsl/utils/font_manager.dart';

class WebMyTransactionsWidgets extends StatefulWidget {
  const WebMyTransactionsWidgets({super.key});

  @override
  State<WebMyTransactionsWidgets> createState() =>
      _WebMyTransactionsWidgetsState();
}

class _WebMyTransactionsWidgetsState extends State<WebMyTransactionsWidgets> {
  bool showPopup = false;
  dynamic selectedItem;

  int currentPageTwo = 0;
  int currentPageSeven = 0;
  final int itemsPerPage = 4;

  bool showGlobalModal = false;
  String? selectedCardKey;

  bool _showSearchBox = false;

  // Sample data for the priority boxes
  final List<String> taskPriorityTwo = [
    "Task Priority 2 Days 05",
    "User Management Workflow",
    "Task Management Workflow",
    "Task Management Workflow",
    "User Management Workflow",
    "Task Management Workflow"
  ];

  final List<String> taskPrioritySeven = [
    "Task Priority 7 Days 12",
    "User Management Workflow",
    "Task Management Workflow",
    "Task Management Workflow",
    "User Management Workflow",
    "Task Management Workflow"
  ];

  void openGlobalModal() {
    setState(() {
      showGlobalModal = true;
    });
  }

  void closeGlobalModal() {
    setState(() {
      showGlobalModal = false;
      // Reset card selection when closing modal
      selectedCardKey = null;
    });
  }

  double _getResponsiveModalHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Calculate maximum modal height as percentage of screen height
    double maxHeightPercentage;
    double topOffset;
    double bottomOffset;

    if (screenWidth >= 1920) {
      // For 4K and large screens
      maxHeightPercentage = 0.75; // 75% of screen height
      topOffset = 60;
      bottomOffset = 60;
    } else if (screenWidth >= 1366) {
      // For standard desktop screens
      maxHeightPercentage = 0.80; // 80% of screen height
      topOffset = 50;
      bottomOffset = 50;
    } else if (screenWidth >= 1100) {
      // For smaller desktop/tablet screens
      maxHeightPercentage = 0.85; // 85% of screen height
      topOffset = 40;
      bottomOffset = 40;
    } else {
      // For mobile/small screens
      maxHeightPercentage = 0.90; // 90% of screen height
      topOffset = 30;
      bottomOffset = 30;
    }

    // Calculate the maximum height
    double maxHeight = screenHeight * maxHeightPercentage;

    // Ensure we don't exceed screen bounds with offsets
    double availableHeight = screenHeight - topOffset - bottomOffset;

    // Return the smaller of the two to ensure modal fits on screen
    return maxHeight < availableHeight ? maxHeight : availableHeight;
  }

  double _getResponsiveModalWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1920) {
      return 700; // Larger modal for 4K screens
    } else if (screenWidth >= 1366) {
      return 660; // Standard desktop size
    } else if (screenWidth >= 1024) {
      return 600; // Smaller desktop/tablet
    } else {
      return screenWidth * 0.9; // 90% of screen width for mobile
    }
  }

  double _getResponsiveModalTopOffset(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1920) {
      return screenHeight * 0.08; // 8% from top for large screens
    } else if (screenWidth >= 1366) {
      return screenHeight * 0.06; // 6% from top for standard desktop
    } else if (screenWidth >= 1024) {
      return screenHeight * 0.05; // 5% from top for smaller desktop
    } else {
      return screenHeight * 0.03; // 3% from top for mobile
    }
  }

  void _toggleSearchBox() {
    setState(() {
      _showSearchBox = !_showSearchBox;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFEDEDED),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Top section with proper alignment
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: SizedBox(
                    height: 240,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "My Transactions",
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      MediaQuery.of(context).size.width >= 1600
                                          ? FontManager.s20
                                          : FontManager.s16,
                                  fontWeight: FontManager.bold,
                                  fontFamily: FontManager.fontFamilyInter,
                                  color: const Color(0xFF606060),
                                ),
                              ),
                              SizedBox(
                                width: 324,
                                height: 36,
                                child: Stack(
                                  alignment: Alignment.centerRight,
                                  children: [
                                    AnimatedOpacity(
                                      duration:
                                          const Duration(milliseconds: 250),
                                      opacity: _showSearchBox ? 1.0 : 0.0,
                                      child: Visibility(
                                        visible: _showSearchBox,
                                        child: TextField(
                                          autofocus: true,
                                          cursorHeight: 16,
                                          decoration: InputDecoration(
                                            hintText: "Search",
                                            filled: true,
                                            fillColor: Colors.white,
                                            contentPadding:
                                                const EdgeInsets.only(
                                                    left: 10, right: 35),
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      right: 3,
                                      child: InkWell(
                                        onTap: _toggleSearchBox,
                                        child: Icon(
                                            _showSearchBox
                                                ? Icons.close
                                                : Icons.search,
                                            size: 20,
                                            color: const Color(0xFF7E7D7D)),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 6),
                        // Dashboard cards row
                        Expanded(
                          child: LayoutBuilder(
                            builder: (context, constraints) {
                              // Calculate available width for cards
                              double availableWidth = constraints.maxWidth;
                              double screenWidth =
                                  MediaQuery.of(context).size.width;

                              // For small screens (< 1250px), use fixed minimum widths to prevent overlapping
                              if (screenWidth < 1250) {
                                return SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    children: [
                                      _buildBox(
                                          context,
                                          taskPriorityTwo,
                                          itemsPerPage,
                                          currentPageTwo, (newPage) {
                                        setState(() {
                                          currentPageTwo = newPage;
                                        });
                                      },
                                          customWidth:
                                              280), // Fixed width for small screens
                                      const SizedBox(width: 14),
                                      _buildBox(
                                          context,
                                          taskPrioritySeven,
                                          itemsPerPage,
                                          currentPageSeven, (newPage) {
                                        setState(() {
                                          currentPageSeven = newPage;
                                        });
                                      },
                                          customWidth:
                                              280), // Fixed width for small screens
                                      const SizedBox(width: 14),
                                      _buildBoxPieChart(
                                          context,
                                          "Status of all Solutions",
                                          "Total 100",
                                          customWidth:
                                              200), // Fixed width for small screens
                                      const SizedBox(width: 14),
                                      _buildBoxLineChart(
                                          "Work-in-progress Tasks Timeline",
                                          customWidth:
                                              350), // Fixed width for small screens
                                    ],
                                  ),
                                );
                              }

                              // For larger screens (>= 1250px), use proportional widths
                              // Total spacing: 3 gaps × 14px = 42px
                              double totalSpacing = 3 * 14;
                              double remainingWidth =
                                  availableWidth - totalSpacing;

                              // Define proportions for each card type - adjusted for better fit at 1220px
                              double taskBoxProportion = screenWidth >= 1400
                                  ? 0.25
                                  : 0.24; // Slightly smaller for mid-range screens
                              double pieChartProportion = screenWidth >= 1400
                                  ? 0.20
                                  : 0.18; // Smaller pie chart for mid-range
                              double lineChartProportion = screenWidth >= 1400
                                  ? 0.30
                                  : 0.34; // Larger line chart to fill space

                              // Calculate actual widths
                              double taskBoxWidth =
                                  remainingWidth * taskBoxProportion;
                              double pieChartWidth =
                                  remainingWidth * pieChartProportion;
                              double lineChartWidth =
                                  remainingWidth * lineChartProportion;

                              // Ensure minimum widths with better constraints for 1220px
                              taskBoxWidth =
                                  taskBoxWidth < 240 ? 240 : taskBoxWidth;
                              pieChartWidth =
                                  pieChartWidth < 170 ? 170 : pieChartWidth;
                              lineChartWidth =
                                  lineChartWidth < 280 ? 280 : lineChartWidth;

                              // Special handling for 1220px screen to prevent gaps
                              if (screenWidth >= 1220 && screenWidth <= 1280) {
                                double totalMinWidth = 240 +
                                    240 +
                                    170 +
                                    280 +
                                    (3 * 14); // min widths + spacing
                                if (availableWidth < totalMinWidth) {
                                  // Use horizontal scroll for tight spaces
                                  return SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Row(
                                      children: [
                                        _buildBox(
                                            context,
                                            taskPriorityTwo,
                                            itemsPerPage,
                                            currentPageTwo, (newPage) {
                                          setState(() {
                                            currentPageTwo = newPage;
                                          });
                                        }, customWidth: 240),
                                        const SizedBox(width: 14),
                                        _buildBox(
                                            context,
                                            taskPrioritySeven,
                                            itemsPerPage,
                                            currentPageSeven, (newPage) {
                                          setState(() {
                                            currentPageSeven = newPage;
                                          });
                                        }, customWidth: 240),
                                        const SizedBox(width: 14),
                                        _buildBoxPieChart(
                                            context,
                                            "Status of all Solutions",
                                            "Total 100",
                                            customWidth: 170),
                                        const SizedBox(width: 14),
                                        _buildBoxLineChart(
                                            "Work-in-progress Tasks Timeline",
                                            customWidth: 280),
                                      ],
                                    ),
                                  );
                                } else {
                                  // Distribute available space proportionally
                                  taskBoxWidth =
                                      (availableWidth - totalSpacing) * 0.24;
                                  pieChartWidth =
                                      (availableWidth - totalSpacing) * 0.18;
                                  lineChartWidth =
                                      (availableWidth - totalSpacing) * 0.34;
                                }
                              }

                              return Row(
                                children: [
                                  _buildBox(context, taskPriorityTwo,
                                      itemsPerPage, currentPageTwo, (newPage) {
                                    setState(() {
                                      currentPageTwo = newPage;
                                    });
                                  }, customWidth: taskBoxWidth),
                                  const SizedBox(width: 14),
                                  _buildBox(
                                      context,
                                      taskPrioritySeven,
                                      itemsPerPage,
                                      currentPageSeven, (newPage) {
                                    setState(() {
                                      currentPageSeven = newPage;
                                    });
                                  }, customWidth: taskBoxWidth),
                                  const SizedBox(width: 14),
                                  _buildBoxPieChart(context,
                                      "Status of all Solutions", "Total 100",
                                      customWidth: pieChartWidth),
                                  const SizedBox(width: 14),
                                  _buildBoxLineChart(
                                      "Work-in-progress Tasks Timeline",
                                      customWidth: lineChartWidth),
                                ],
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 18),
                // Bottom section with workflow cards
                TabChartSection(
                  onOpenModal: openGlobalModal,
                ),
              ],
            ),
          ),
          if (showGlobalModal)
            Positioned.fill(
              child: GestureDetector(
                onTap: closeGlobalModal,
                child: Container(color: Colors.black.withOpacity(0.5)),
              ),
            ),
          if (showGlobalModal)
            Positioned(
              left: MediaQuery.of(context).size.width / 2 -
                  _getResponsiveModalWidth(context) / 2,
              top: _getResponsiveModalTopOffset(context),
              child: Container(
                width: _getResponsiveModalWidth(context),
                constraints: BoxConstraints(
                  maxHeight: _getResponsiveModalHeight(context),
                  minHeight:
                      300, // Reduced minimum height for better responsiveness
                ),
                decoration: BoxDecoration(
                  color: Color(0xFFF8F9FA),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x4ACDD7E8),
                      blurRadius: 10,
                      offset: Offset(0, 3),
                    ),
                  ],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _buildGlobalModalContent(),
              ),
            ),
        ],
      ),
    );
  }

  // Helper method to build card rows in the popup
  Widget _buildCardRow(String title, String assignedText, String date,
      String loText, String status) {
    return Container(
      width: double.infinity,
      height: 80,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xFFE4E4E4)),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: Offset(0, 1),
            blurRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title row with icon
          Row(
            children: [
              Container(
                width: 16,
                height: 16,
                padding: EdgeInsets.all(2),
                child: SvgPicture.asset('assets/images/usermanagement.svg'),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Inter',
                    color: Color(0xFF606060),
                  ),
                ),
              ),
            ],
          ),
          Spacer(),
          // Bottom row with assignment info and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Updated on:",
                    style: TextStyle(fontSize: 10, color: Color(0xFFB3B3B3)),
                  ),
                  Text(
                    date,
                    style: TextStyle(fontSize: 11, color: Color(0xFF606060)),
                  ),
                ],
              ),
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 20,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Color(0xFFE4E4E4)),
                    ),
                    child: Text(
                      loText,
                      style: TextStyle(fontSize: 10, color: Colors.blue),
                    ),
                  ),
                  SizedBox(width: 4),
                  Container(
                    width: 64,
                    height: 20,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: status == "Pending"
                          ? Color(0xFFFEF7E2)
                          : Color(0xFFE0F7E9),
                      borderRadius: BorderRadius.circular(13),
                    ),
                    child: Text(
                      status,
                      style: TextStyle(
                        fontSize: 10,
                        color: status == "Pending"
                            ? Color.fromRGBO(206, 143, 0, 1)
                            : Color.fromRGBO(0, 125, 80, 1),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to build small card rows for the 3x3 grid in popup
  Widget _buildCardRowSmall(
      String title, String date, String loText, String status) {
    return Container(
      height: 60,
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xFFE4E4E4)),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title row with icon
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                padding: EdgeInsets.all(1),
                child: SvgPicture.asset('assets/images/usermanagement.svg'),
              ),
              SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Inter',
                    color: Color(0xFF606060),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          Spacer(),
          // Bottom row with assignment info and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                date,
                style: TextStyle(fontSize: 8, color: Color(0xFF606060)),
              ),
              Row(
                children: [
                  Container(
                    width: 24,
                    height: 14,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Color(0xFFE4E4E4)),
                    ),
                    child: Text(
                      loText,
                      style: TextStyle(fontSize: 8, color: Colors.blue),
                    ),
                  ),
                  SizedBox(width: 4),
                  Container(
                    width: 48,
                    height: 14,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: status == "Pending"
                          ? Color(0xFFFEF7E2)
                          : Color(0xFFE0F7E9),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      status,
                      style: TextStyle(
                        fontSize: 8,
                        color: status == "Pending"
                            ? Color.fromRGBO(206, 143, 0, 1)
                            : Color.fromRGBO(0, 125, 80, 1),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to build leave request items for the global modal
  Widget _buildLeaveRequestItem(String title, String dateTime, String status) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xFFE4E4E4)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF606060),
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  dateTime,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 12,
                    color: Color(0xFF999999),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color:
                  status == "Pending" ? Color(0xFFFEF7E2) : Color(0xFFE0F7E9),
              borderRadius: BorderRadius.circular(13),
            ),
            child: Text(
              status,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: status == "Pending"
                    ? Color.fromRGBO(206, 143, 0, 1)
                    : Color.fromRGBO(0, 125, 80, 1),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build the global modal content
  Widget _buildGlobalModalContent() {
    return Column(
      children: [
        // Header section
        Container(
          padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Transaction Details",
                style: FontManager.getCustomStyle(
                  fontSize: MediaQuery.of(context).size.width >= 1920
                      ? FontManager.s18
                      : MediaQuery.of(context).size.width >= 1600
                          ? FontManager.s16
                          : FontManager.s14,
                  fontWeight: FontManager.semiBold,
                  fontFamily: FontManager.fontFamilyInter,
                  color: const Color(0xFF606060),
                ),
              ),
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: closeGlobalModal,
                  child: Icon(
                    Icons.close,
                    size: AppThemeConstants.spacingM,
                    color: Color(0xFF606060),
                  ),
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
          child: const Divider(
            color: Color(0xFFE4E4E4),
            thickness: 1,
            height: 0,
          ),
        ),
        // Content section
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Solution Name and Date
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Solution Name',
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontSize: MediaQuery.of(context).size.width >= 2000
                                ? FontManager.s14
                                : MediaQuery.of(context).size.width >= 1600
                                    ? FontManager.s12
                                    : FontManager.s10,
                            fontWeight: FontManager.medium,
                            color: const Color(0xFFB3B3B3),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Leave Management',
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontSize: MediaQuery.of(context).size.width >= 2000
                                ? FontManager.s16
                                : MediaQuery.of(context).size.width >= 1600
                                    ? FontManager.s14
                                    : FontManager.s12,
                            fontWeight: FontManager.semiBold,
                            color: Color(0xFF3379FF),
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'April 13, 2025',
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontSize: MediaQuery.of(context).size.width >= 2000
                                ? FontManager.s14
                                : MediaQuery.of(context).size.width >= 1600
                                    ? FontManager.s12
                                    : FontManager.s12,
                            fontWeight: FontManager.semiBold,
                            color: const Color(0xFF606060),
                          ),
                        ),
                        SizedBox(height: 2),
                        Text(
                          '3:30 PM',
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontSize: MediaQuery.of(context).size.width >= 2000
                                ? FontManager.s14
                                : MediaQuery.of(context).size.width >= 1600
                                    ? FontManager.s12
                                    : FontManager.s12,
                            fontWeight: FontManager.semiBold,
                            color: const Color(0xFF606060),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 20),

                // Task Name and Transaction ID
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Task Name',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: MediaQuery.of(context).size.width >=
                                      2000
                                  ? FontManager.s14
                                  : MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            'Leave Request',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: MediaQuery.of(context).size.width >=
                                      2000
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s14
                                      : FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Transaction ID',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: MediaQuery.of(context).size.width >=
                                      2000
                                  ? FontManager.s14
                                  : MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 6),
                          Text(
                            '987654321e-76543-2345-2',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 2000
                                      ? FontManager.s16
                                      : FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // SizedBox(height: 10),
                Divider(
                  color: Color(0xFFE4E4E4),
                  thickness: 1,
                  height: 24,
                ),

                // Leave Request with Approved status
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Leave Request',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: MediaQuery.of(context).size.width >= 2000
                            ? FontManager.s16
                            : FontManager.s14,
                        fontWeight: FontManager.bold,
                        color: Color(0xFF000000),
                      ),
                    ),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 14, vertical: 4),
                      decoration: BoxDecoration(
                        color: Color(0xFFA8FCDB),
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: Text(
                        'Approved',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s14
                              : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: Color(0xFF008B60),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20),

                // Reason
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Reason',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: MediaQuery.of(context).size.width >= 1600
                            ? FontManager.s12
                            : FontManager.s10,
                        fontWeight: FontManager.medium,
                        color: const Color(0xFFB3B3B3),
                      ),
                    ),
                    SizedBox(height: 2),
                    Text(
                      'Illness and health-related and Medical appointments',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: FontManager.s14,
                        fontWeight: FontManager.semiBold,
                        color: Color(0xFF606060),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20),

                // Three-column layout for Leave Request details
                Row(
                  children: [
                    // Column 1: Leave Type
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Leave Type',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            'Sick Leave',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Column 2: Duration in days
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Duration in days',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            '1',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Column 3: Request ID
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Request ID',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            '5432234',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20),
                // Second row: Employee ID, Start Date, End Date
                Row(
                  children: [
                    // Column 1: Employee ID
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Employee ID',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            '5432-asy-234',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Column 2: Start Date
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Start Date',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            'April 13, 2025',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Column 3: End Date
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'End Date',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            'April 13, 2025',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 2),
                Divider(
                  color: Color(0xFFE4E4E4),
                  thickness: 1,
                  height: 24,
                ),
                SizedBox(height: 2),
                // Document section
                Text(
                  'Document',
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.bold,
                    color: Color(0xFF000000),
                  ),
                ),
                SizedBox(height: 20),

                // Three-column layout for Document details
                Row(
                  children: [
                    // Column 1: File Name
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'File Name',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            'Leave Request v01',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Column 2: File Type
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'File Type',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            'PDF',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Column 3: Upload Date
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Upload Date',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            'April 13, 2025',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 15),

                // Second row: Document ID and Request ID
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Column 1: Document ID
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Document ID',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            '5432-asy-234-5432-asy-234',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Column 2: Empty space
                    Expanded(child: SizedBox()),
                    // Column 3: Request ID
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Request ID',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize:
                                  MediaQuery.of(context).size.width >= 1600
                                      ? FontManager.s12
                                      : FontManager.s10,
                              fontWeight: FontManager.medium,
                              color: const Color(0xFFB3B3B3),
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            '5432-asy-234-5432-asy',
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontSize: FontManager.s14,
                              fontWeight: FontManager.semiBold,
                              color: Color(0xFF606060),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 10),
                Divider(
                  color: Color(0xFFE4E4E4),
                  thickness: 1,
                  height: 24,
                ),
                // Note section
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: 'Note: ',
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontSize: FontManager.s14,
                            fontWeight: FontManager.semiBold,
                            color: Color(0xFF606060),
                          ),
                        ),
                        TextSpan(
                          text:
                              'Calculate Business Days Check Leave Balance Validate Leave Policy Compliance',
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontSize: MediaQuery.of(context).size.width >= 1600
                                ? FontManager.s14
                                : FontManager.s12,
                            fontWeight: FontManager.medium,
                            color: const Color(0xFFB3B3B3),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

Widget _buildBox(BuildContext context, List<String> taskPriority,
    int itemsPerPage, int currentPage, Function(int) onPageChange,
    {double? customWidth}) {
  int totalPages = ((taskPriority.length - 1) / itemsPerPage).ceil();
  final displayItems = taskPriority.length > 1
      ? taskPriority.sublist(
          1 + currentPage * itemsPerPage,
          (1 + (currentPage + 1) * itemsPerPage).clamp(1, taskPriority.length),
        )
      : [];

  final parts = taskPriority[0].split(" ");
  final titleText = parts.length > 2
      ? parts.sublist(0, parts.length - 2).join(" ")
      : taskPriority[0];
  final countText =
      parts.length >= 2 ? parts.sublist(parts.length - 2).join(" ") : '';

  return ConstrainedBox(
    constraints: BoxConstraints(
      minHeight: MediaQuery.of(context).size.width >= 1600
          ? 244
          : 185, // responsive height based on screen width
      maxWidth: customWidth ?? 298, // responsive width
    ),
    child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: SizedBox(
        height: MediaQuery.of(context).size.width >= 1600
            ? 244
            : 185, // responsive height
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 12, left: 16),
                  child: SizedBox(
                    width: 146,
                    height: 17,
                    child: Text(
                      titleText,
                      style: FontManager.getCustomStyle(
                        fontWeight: FontManager.semiBold,
                        fontSize: MediaQuery.of(context).size.width >= 1600
                            ? FontManager.s18
                            : FontManager.s14,
                        fontFamily: FontManager.fontFamilyInter,
                        color: const Color(0xFF606060),
                        height: 16 / 14,
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 12, right: 16),
                  child: Text(
                    countText,
                    style: FontManager.getCustomStyle(
                      fontWeight: FontManager.bold,
                      fontSize: MediaQuery.of(context).size.width >= 1600
                          ? FontManager.s24
                          : FontManager.s18,
                      fontFamily: FontManager.fontFamilyInter,
                      height: 21 / 18,
                      color: taskPriority[0].contains("2 Days")
                          ? const Color(0xFFF47B74)
                          : const Color(0xFFF5BA76),
                    ),
                  ),
                ),
              ],
            ),

            // Items
            ...displayItems.map((item) {
              return Padding(
                padding: const EdgeInsets.only(left: 16, top: 8, bottom: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: taskPriority[0].contains("2 Days")
                                ? const Color(0xFFF47B74)
                                : const Color(0xFFFECA8E),
                          ),
                          child: Center(
                            child: Transform.rotate(
                              angle: -0.785398,
                              child: const Icon(
                                Icons.arrow_forward,
                                size: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          item,
                          style: FontManager.getCustomStyle(
                            fontSize: MediaQuery.of(context).size.width >= 1440
                                ? FontManager.s14
                                : FontManager.s12,
                            color: const Color(0xFF606060),
                            fontFamily: FontManager.fontFamilyInter,
                            height: 1.25,
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: SvgPicture.asset(
                        'assets/images/arrow-right.svg',
                        width: 12,
                        height: 12,
                        colorFilter: const ColorFilter.mode(
                          Color(0xFF707070),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),

            // Push pagination to the bottom even if less items
            const Spacer(), // ✅ Pushes pagination to bottom safely

            // Pagination
            // Pagination section
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildCircleArrowButton(
                      icon: Icons.arrow_back,
                      onTap: currentPage > 0
                          ? () => onPageChange(currentPage - 1)
                          : null,
                    ),
                    for (int i = 0; i < totalPages; i++)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 2),
                        child: Container(
                          width: 4,
                          height: 4,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: i == currentPage
                                ? const Color(0xFF242424)
                                : Colors.transparent,
                            border: i == currentPage
                                ? null
                                : Border.all(
                                    color: const Color(0xFF242424)
                                        .withOpacity(0.2),
                                  ),
                          ),
                        ),
                      ),
                    _buildCircleArrowButton(
                      icon: Icons.arrow_forward,
                      onTap: currentPage < totalPages - 1
                          ? () => onPageChange(currentPage + 1)
                          : null,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

Widget _buildCircleArrowButton({required IconData icon, VoidCallback? onTap}) {
  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(12),
    child: SizedBox(
      width: 24,
      height: 24,
      child: Center(
        child: Icon(
          icon,
          size: 12,
          color: onTap != null ? Colors.black : Colors.grey.shade400,
        ),
      ),
    ),
  );
}

Widget _buildBoxPieChart(BuildContext context, String heading, String content,
    {double? customWidth}) {
  final double completed = 56;
  final double pending = 44;

  final Color completedColor = const Color.fromRGBO(146, 222, 163, 1);
  final Color pendingColor = const Color.fromRGBO(253, 238, 224, 1);

  return SizedBox(
    width: customWidth ?? 210,
    height: 188,
    child: Container(
      // padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white, // background
        borderRadius: BorderRadius.circular(12), // border-radius: 12px
        boxShadow: [
          BoxShadow(
            color: const Color(0x1A000000), // #0000001A (10% black)
            offset: const Offset(0, 2), // 0px 2px
            blurRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 16, left: 16, bottom: 10.91),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                heading,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12, // correct as per UX
                  height: 15 / 12, // Line height = 15px
                  fontFamily: 'Inter',
                  letterSpacing: 0.0,
                  color: Color(0xFF606060),
                ),
              ),
            ),
          ),

          //const SizedBox(height: 4),
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                f1.PieChart(
                  f1.PieChartData(
                    sectionsSpace: 0,
                    centerSpaceRadius: 30,
                    sections: [
                      f1.PieChartSectionData(
                        value: completed,
                        title: completed.toInt().toString(),
                        color: completedColor,
                        radius: 18,
                        titleStyle: const TextStyle(
                          fontSize: 9,
                          //fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        titlePositionPercentageOffset: 0.5,
                      ),
                      f1.PieChartSectionData(
                        value: pending,
                        title: pending.toInt().toString(),
                        color: pendingColor,
                        radius: 18,
                        titleStyle: const TextStyle(
                          fontSize: 9,
                          //  fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        titlePositionPercentageOffset: 0.5,
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Total',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                        height: 1,
                        fontFamily: 'Inter',
                        letterSpacing: 0,
                        color: Color.fromRGBO(170, 169, 169, 1),
                      ),
                    ),
                    Text(
                      content.replaceAll(RegExp(r'^[^\d]*'), ''),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        height: 17 / 14,
                        fontFamily: 'Inter',
                        letterSpacing: 0,
                        color: Color.fromRGBO(96, 96, 96, 1),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding:
                EdgeInsets.only(bottom: 13), // Simulate `bottom: 13` spacing
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegend(color: pendingColor, label: "Pending"),
                const SizedBox(width: 12),
                _buildLegend(color: completedColor, label: "Completed"),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildBoxLineChart(String heading, {double? customWidth}) {
  return SizedBox(
    width: customWidth ?? 420,
    height: 188,
    child: Container(
      padding: const EdgeInsets.only(left: 16, top: 8, right: 12, bottom: 18.5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A000000),
            offset: Offset(0, 2),
            blurRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and filter row unchanged
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 185,
                child: Text(
                  heading,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Inter',
                    color: Color(0xFF606060),
                  ),
                ),
              ),
              const Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 16),
                    child: Row(
                      children: [
                        Text("5D | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("1M | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("3M | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("6M | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("1Y",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                              decoration: TextDecoration.underline,
                            )),
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
          const SizedBox(height: 10),

          // Chart area + "Hrs" label below
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 11, // Increased flex for chart to take more space
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: 4,
                        right: 4,
                        top: 0,
                        bottom: 0), // added bottom padding to push chart down
                    child: f1.LineChart(
                      f1.LineChartData(
                        minX: 0,
                        maxX: 12,
                        minY: 0,
                        maxY: 180,
                        gridData: f1.FlGridData(
                          show: true,
                          drawVerticalLine: true,
                          drawHorizontalLine: true,
                          horizontalInterval: 45,
                          verticalInterval: 1,
                          checkToShowHorizontalLine: (value) =>
                              value == 0 ||
                              value == 45 ||
                              value == 90 ||
                              value == 135 ||
                              value == 180,
                          checkToShowVerticalLine: (value) =>
                              value >= 0 && value <= 12,
                          getDrawingHorizontalLine: (value) => f1.FlLine(
                            color: const Color(0xFFCCCCCC),
                            strokeWidth: 0.3,
                          ),
                          getDrawingVerticalLine: (value) => f1.FlLine(
                            color: const Color(0xFFCCCCCC),
                            strokeWidth: 0.3,
                          ),
                        ),
                        borderData: f1.FlBorderData(
                          show: true,
                          border: const Border(
                            left: BorderSide(
                                color: Color(0xFFCCCCCC), width: 0.3),
                            bottom: BorderSide(
                                color: Color(0xFFCCCCCC), width: 0.3),
                            top: BorderSide(
                                color: Color(0xFFCCCCCC),
                                width: 0.3), // now visible
                            right: BorderSide(
                                color: Color(0xFFCCCCCC),
                                width: 0.3), // now visible
                          ),
                        ),
                        titlesData: f1.FlTitlesData(
                          topTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(showTitles: false),
                          ),
                          rightTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(showTitles: false),
                          ),
                          bottomTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(
                              showTitles: true,
                              reservedSize: 14,
                              interval: 0.5,
                              getTitlesWidget: (value, meta) {
                                const months = [
                                  'Ap',
                                  'Ma',
                                  'Jn',
                                  'Ju',
                                  'Au',
                                  'Se',
                                  'Oc',
                                  'No',
                                  'De',
                                  'Ja',
                                  'Fe',
                                  'Ma',
                                ];
                                if (value % 1 == 0.5) {
                                  int index = value.floor();
                                  if (index >= 0 && index < months.length) {
                                    return Padding(
                                      padding: const EdgeInsets.only(
                                          left:
                                              8), // shifted right to center month label
                                      child: Text(
                                        months[index],
                                        style: const TextStyle(fontSize: 10),
                                      ),
                                    );
                                  }
                                }
                                return const SizedBox.shrink();
                              },
                            ),
                          ),
                          leftTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(
                              showTitles: true,
                              reservedSize: 18,
                              interval: 45,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  '${value.toInt()}',
                                  style: const TextStyle(fontSize: 10),
                                );
                              },
                            ),
                          ),
                        ),
                        lineBarsData: [
                          f1.LineChartBarData(
                            isCurved: true,
                            curveSmoothness: 0.6,
                            spots: const [
                              f1.FlSpot(0, 45),
                              f1.FlSpot(2, 55),
                              f1.FlSpot(4, 39),
                              f1.FlSpot(6, 60),
                              f1.FlSpot(8, 170),
                              f1.FlSpot(11, 45),
                              f1.FlSpot(12, 3),
                            ],
                            barWidth: 1,
                            color: const Color(0xFF66BA04),
                            dotData: f1.FlDotData(show: false),
                          ),
                          f1.LineChartBarData(
                            isCurved: true,
                            curveSmoothness: 0.6,
                            spots: const [
                              f1.FlSpot(0, 90),
                              f1.FlSpot(2, 97),
                              f1.FlSpot(4, 120),
                              f1.FlSpot(6, 50),
                              f1.FlSpot(9, 140),
                              f1.FlSpot(11, 10),
                              f1.FlSpot(12, 170),
                            ],
                            barWidth: 1,
                            color: const Color(0xFF00008B),
                            dotData: f1.FlDotData(show: false),
                          ),
                          f1.LineChartBarData(
                            isCurved: true,
                            curveSmoothness: 0.6,
                            spots: const [
                              f1.FlSpot(0, 20),
                              f1.FlSpot(2, 70),
                              f1.FlSpot(5, 140),
                              f1.FlSpot(7, 100),
                              f1.FlSpot(9, 110),
                              f1.FlSpot(11, 133),
                              f1.FlSpot(12, 160),
                            ],
                            barWidth: 1,
                            color: const Color(0xFFD862FC),
                            dotData: f1.FlDotData(show: false),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // "Hrs" label at bottom-left corner with minimal padding
                Expanded(
                  flex: 0,
                  child: Align(
                    alignment: Alignment.bottomLeft,
                    child: Text(
                      'Hrs',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Inter',
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildLegend({required Color color, required String label}) {
  return Row(
    children: [
      Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color,
        ),
      ),
      const SizedBox(width: 4),
      Text(
        label,
        style: const TextStyle(fontSize: 12, color: Colors.black),
      ),
    ],
  );
}

class TabChartSection extends StatefulWidget {
//  const TabChartSection({super.key});
  final VoidCallback onOpenModal;

  const TabChartSection({super.key, required this.onOpenModal});

  @override
  State<TabChartSection> createState() => _TabChartSectionState();
}

class _TabChartSectionState extends State<TabChartSection> {
  List<WorkflowItem> allData = [];
  List<WorkflowItem> filteredData = [];
  String? popupType; // "Completed" or "Pending"

  String selectedTab = 'All';
  int currentPage = 1;
  int itemsPerPage = 12;
  int totalPages = 1;

  bool showPopup = false;
  WorkflowItem? selectedItem;
  bool showCenterModal = false;

  // Add selected card tracking (only one card can be selected)
  String? selectedCardKey;

  // Add GlobalKey to measure card grid height
  final GlobalKey _cardGridKey = GlobalKey();
  final GlobalKey _fullCardSectionKey = GlobalKey();
  double _cardGridHeight = 0;
  double _fullCardSectionHeight = 0;

  @override
  void initState() {
    super.initState();
    loadData();
  }

  void loadData() async {
    final String jsonString =
        await rootBundle.loadString('assets/data/workflows.json');
    final List<dynamic> jsonResponse = json.decode(jsonString);

    setState(() {
      allData =
          jsonResponse.map((data) => WorkflowItem.fromJson(data)).toList();
      applyFilter();
    });
  }

  void applyFilter() {
    setState(() {
      if (selectedTab == 'All') {
        filteredData = allData;
      } else {
        filteredData =
            allData.where((item) => item.status == selectedTab).toList();
      }
      totalPages = (filteredData.length / itemsPerPage).ceil();
      currentPage = 1;
    });
  }

  void nextPage() {
    if (currentPage < totalPages) {
      setState(() {
        currentPage++;
      });
    }
  }

  void prevPage() {
    if (currentPage > 1) {
      setState(() {
        currentPage--;
      });
    }
  }

  List<WorkflowItem> get paginatedData {
    int start = (currentPage - 1) * itemsPerPage;
    int end = start + itemsPerPage;

    // When popup is open, limit to 9 items (3 rows × 3 cards)
    if (showPopup) {
      int maxItems = 9;
      end = start + maxItems;
    }

    return filteredData.sublist(
      start,
      end > filteredData.length ? filteredData.length : end,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildMainContent(), // Your main UI (tabs, charts, etc.)
      ],
    );
  }

  Widget _buildPopup() {
    if (selectedItem == null || popupType == null) return SizedBox.shrink();

    // Calculate popup height based on card grid height only
    double popupHeight = _cardGridHeight > 0
        ? _cardGridHeight + 70 // Add small padding for better alignment
        : MediaQuery.of(context).size.height - 120;

    // Ensure minimum and maximum constraints
    popupHeight =
        popupHeight.clamp(400.0, MediaQuery.of(context).size.height - 120);

    return Container(
      height: popupHeight,
      margin: const EdgeInsets.only(left: 16, top: 0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0x4ACDD7E8),
            blurRadius: 10,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content
          Flexible(
            child: popupType == 'Completed'
                ? _buildPopupDataCompleted()
                : _buildPopupDataPending(),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      color: const Color.fromRGBO(245, 245, 245, 1),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 4,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LayoutBuilder(
                    builder: (context, constraints) {
                      bool isSmallScreen = constraints.maxWidth < 500;
                      Widget tabSection = isSmallScreen
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Wrap(
                                  spacing: 10,
                                  runSpacing: 10,
                                  children: [
                                    _buildTab("All"),
                                    _buildTab("Pending"),
                                    _buildTab("Completed"),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: _buildPaginationControls(),
                                ),
                              ],
                            )
                          : Row(
                              children: [
                                Expanded(
                                  child: Row(
                                    children: [
                                      _buildTab("All"),
                                      const SizedBox(width: 12),
                                      _buildTab("Pending"),
                                      const SizedBox(width: 12),
                                      _buildTab("Completed"),
                                    ],
                                  ),
                                ),
                                _buildPaginationControls(),
                              ],
                            );
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 20),
                            child: tabSection,
                          ),
                          const Divider(
                            color: Color(0xFFEDEDED),
                            thickness: 1.0,
                            height: 0.5,
                          ),
                        ],
                      );
                    },
                  ),
                  const SizedBox(height: 20),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: showPopup ? 70 : 100,
                        child: _buildChartContent(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 40),
                ],
              ),
            ),
            if (showPopup && selectedItem != null)
              Expanded(
                flex: 2,
                child: _buildPopup(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopupDataCompleted() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Transaction Details',
                  style: FontManager.getCustomStyle(
                    fontSize: MediaQuery.of(context).size.width >= 2500
                        ? FontManager.s18
                        : MediaQuery.of(context).size.width >= 1920
                            ? FontManager.s16
                            : MediaQuery.of(context).size.width >= 1366
                                ? FontManager.s14
                                : FontManager.s12,
                    fontWeight: FontManager.bold,
                    color: Color(0xFF606060),
                  ),
                ),
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        showPopup = false;
                        selectedItem = null;
                        popupType = null;
                        selectedCardKey = null;
                      });
                    },
                    child: Icon(
                      Icons.close,
                      size: AppThemeConstants.iconSizeM,
                      color: Color(0xFF606060),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: AppThemeConstants.spacingS),
            Divider(
              color: Color(0xFFE4E4E4),
              thickness: 1,
              height: 0,
            ),
            SizedBox(height: AppThemeConstants.spacingL),

            // Solution Name and Date row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Solution Name',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: MediaQuery.of(context).size.width >= 2000
                            ? FontManager.s16
                            : MediaQuery.of(context).size.width >= 1920
                                ? FontManager.s14
                                : MediaQuery.of(context).size.width >= 1366
                                    ? FontManager.s12
                                    : MediaQuery.of(context).size.width >= 1200
                                        ? FontManager.s10
                                        : FontManager.s10,
                        fontWeight: FontManager.medium,
                        color: const Color(0xFFB3B3B3),
                      ),
                    ),
                    SizedBox(height: 2),
                    Text(
                      'Leave Management',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: MediaQuery.of(context).size.width >= 2000
                            ? FontManager.s18
                            : MediaQuery.of(context).size.width >= 1920
                                ? FontManager.s16
                                : MediaQuery.of(context).size.width >= 1366
                                    ? FontManager.s14
                                    : MediaQuery.of(context).size.width >= 1200
                                        ? FontManager.s12
                                        : FontManager.s12,
                        fontWeight: FontManager.semiBold,
                        color: Color(0xFF3379FF),
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'April 30, 2025',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: MediaQuery.of(context).size.width >= 2000
                            ? FontManager.s18
                            : MediaQuery.of(context).size.width >= 1920
                                ? FontManager.s16
                                : MediaQuery.of(context).size.width >= 1366
                                    ? FontManager.s14
                                    : MediaQuery.of(context).size.width >= 1200
                                        ? FontManager.s12
                                        : FontManager.s12,
                        fontWeight: FontManager.semiBold,
                        color: const Color(0xFF606060),
                      ),
                    ),
                    SizedBox(height: 2),
                    Text(
                      '3.30PM',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: MediaQuery.of(context).size.width >= 2000
                            ? FontManager.s18
                            : MediaQuery.of(context).size.width >= 1920
                                ? FontManager.s16
                                : MediaQuery.of(context).size.width >= 1366
                                    ? FontManager.s14
                                    : MediaQuery.of(context).size.width >= 1200
                                        ? FontManager.s12
                                        : FontManager.s12,
                        fontWeight: FontManager.semiBold,
                        color: const Color(0xFF606060),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 16),

            // Task Name and Transaction ID row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Task Name',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s16
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s14
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s12
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s10
                                          : FontManager.s10,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        'Leave Request',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 15),

            // Transaction ID
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Transaction ID',
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: MediaQuery.of(context).size.width >= 2000
                        ? FontManager.s16
                        : MediaQuery.of(context).size.width >= 1920
                            ? FontManager.s14
                            : MediaQuery.of(context).size.width >= 1366
                                ? FontManager.s12
                                : MediaQuery.of(context).size.width >= 1200
                                    ? FontManager.s10
                                    : FontManager.s10,
                    fontWeight: FontManager.medium,
                    color: const Color(0xFFB3B3B3),
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '987654321e-76543-2345-2',
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: MediaQuery.of(context).size.width >= 2000
                        ? FontManager.s18
                        : MediaQuery.of(context).size.width >= 1920
                            ? FontManager.s16
                            : MediaQuery.of(context).size.width >= 1366
                                ? FontManager.s14
                                : MediaQuery.of(context).size.width >= 1200
                                    ? FontManager.s12
                                    : FontManager.s12,
                    fontWeight: FontManager.semiBold,
                    color: Color(0xFF606060),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),

            Divider(
              color: Color(0xFFE4E4E4),
              thickness: 1,
              height: 0,
            ),
            SizedBox(height: 10),

            // Leave Request with Approved status row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Leave Request',
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: MediaQuery.of(context).size.width >= 2000
                        ? FontManager.s18
                        : MediaQuery.of(context).size.width >= 1920
                            ? FontManager.s16
                            : MediaQuery.of(context).size.width >= 1366
                                ? FontManager.s14
                                : MediaQuery.of(context).size.width >= 1200
                                    ? FontManager.s12
                                    : FontManager.s12,
                    fontWeight: FontManager.bold,
                    color: Color(0xFF000000),
                  ),
                ),
                Container(
                  width: 70,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(168, 252, 219, 1),
                    borderRadius: BorderRadius.all(Radius.circular(13)),
                  ),
                  child: Center(
                    child: Text(
                      'Approved',
                      textAlign: TextAlign.center,
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: MediaQuery.of(context).size.width >= 2000
                            ? FontManager.s16
                            : MediaQuery.of(context).size.width >= 1920
                                ? FontManager.s14
                                : MediaQuery.of(context).size.width >= 1366
                                    ? FontManager.s12
                                    : MediaQuery.of(context).size.width >= 1200
                                        ? FontManager.s10
                                        : FontManager.s10,
                        fontWeight: FontManager.medium,
                        color: Color(0xFF008B60),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 15),

            // Reason
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Reason',
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: MediaQuery.of(context).size.width >= 2000
                        ? FontManager.s16
                        : MediaQuery.of(context).size.width >= 1920
                            ? FontManager.s14
                            : MediaQuery.of(context).size.width >= 1366
                                ? FontManager.s12
                                : MediaQuery.of(context).size.width >= 1200
                                    ? FontManager.s10
                                    : FontManager.s10,
                    fontWeight: FontManager.medium,
                    color: const Color(0xFFB3B3B3),
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Illness and health-related and Medical appointments',
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: MediaQuery.of(context).size.width >= 2000
                        ? FontManager.s18
                        : MediaQuery.of(context).size.width >= 1920
                            ? FontManager.s16
                            : MediaQuery.of(context).size.width >= 1366
                                ? FontManager.s14
                                : MediaQuery.of(context).size.width >= 1200
                                    ? FontManager.s12
                                    : FontManager.s12,
                    fontWeight: FontManager.semiBold,
                    color: Color(0xFF606060),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),

            // Leave Type and Duration row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Leave Type',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s16
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s14
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s12
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s10
                                          : FontManager.s10,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Sick Leave',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Duration in days',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s14
                              : MediaQuery.of(context).size.width >= 1600
                                  ? FontManager.s12
                                  : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '1',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 15),

            // Request ID and Employee ID row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Request ID',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s14
                              : MediaQuery.of(context).size.width >= 1600
                                  ? FontManager.s12
                                  : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '5432234',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Employee ID',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s14
                              : MediaQuery.of(context).size.width >= 1600
                                  ? FontManager.s12
                                  : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '5432-asy-234',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 15),

            // Start Date and End Date row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Start Date',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s14
                              : MediaQuery.of(context).size.width >= 1600
                                  ? FontManager.s12
                                  : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'April 13, 2025',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'End Date',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s14
                              : MediaQuery.of(context).size.width >= 1600
                                  ? FontManager.s12
                                  : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'April 13, 2025',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),

            Divider(
              color: Color(0xFFE4E4E4),
              thickness: 1,
              height: 0,
            ),
            SizedBox(height: 10),

            // Document section
            Text(
              'Document',
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontSize: MediaQuery.of(context).size.width >= 2000
                    ? FontManager.s18
                    : MediaQuery.of(context).size.width >= 1920
                        ? FontManager.s16
                        : MediaQuery.of(context).size.width >= 1366
                            ? FontManager.s14
                            : MediaQuery.of(context).size.width >= 1200
                                ? FontManager.s12
                                : FontManager.s12,
                fontWeight: FontManager.bold,
                color: Color(0xFF000000),
              ),
            ),
            SizedBox(height: 15),

            Row(
              children: [
                // Column 1: File Name
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'File Name',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 1919
                              ? FontManager.s14
                              : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        'Leave Request v01',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
                // Column 2: File Type
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'File Type',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 1600
                              ? FontManager.s12
                              : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        'PDF',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
                // Column 3: Upload Date
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Upload Date',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 1600
                              ? FontManager.s12
                              : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        'April 13, 2025',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 15),

            // Second row: Document ID and Request ID
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Column 1: Document ID
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Document ID',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 1600
                              ? FontManager.s12
                              : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        '5432-asy-234-5432-asy-234',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
                // Column 2: Empty space
                Expanded(child: SizedBox()),
                // Column 3: Request ID
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Request ID',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 1600
                              ? FontManager.s12
                              : FontManager.s12,
                          fontWeight: FontManager.medium,
                          color: const Color(0xFFB3B3B3),
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        '5432-asy-234-5432-asy',
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s18
                              : MediaQuery.of(context).size.width >= 1920
                                  ? FontManager.s16
                                  : MediaQuery.of(context).size.width >= 1366
                                      ? FontManager.s14
                                      : MediaQuery.of(context).size.width >=
                                              1200
                                          ? FontManager.s12
                                          : FontManager.s12,
                          fontWeight: FontManager.semiBold,
                          color: Color(0xFF606060),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
            Divider(
              color: Color(0xFFE4E4E4),
              thickness: 1,
              height: 24,
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'Note: ',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: MediaQuery.of(context).size.width >= 2000
                            ? FontManager.s18
                            : MediaQuery.of(context).size.width >= 1920
                                ? FontManager.s16
                                : MediaQuery.of(context).size.width >= 1366
                                    ? FontManager.s14
                                    : MediaQuery.of(context).size.width >= 1200
                                        ? FontManager.s12
                                        : FontManager.s12,
                        fontWeight: FontManager.semiBold,
                        color: Color(0xFF606060),
                      ),
                    ),
                    TextSpan(
                      text:
                          'Calculate Business Days Check Leave Balance Validate Leave Policy Compliance',
                      style: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontSize: MediaQuery.of(context).size.width >= 2000
                            ? FontManager.s18
                            : MediaQuery.of(context).size.width >= 1920
                                ? FontManager.s16
                                : MediaQuery.of(context).size.width >= 1366
                                    ? FontManager.s14
                                    : MediaQuery.of(context).size.width >= 1200
                                        ? FontManager.s12
                                        : FontManager.s12,
                        fontWeight: FontManager.medium,
                        color: const Color(0xFFB3B3B3),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildPopupDataPending() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Transaction Details',
                  style: FontManager.getCustomStyle(
                    fontSize: MediaQuery.of(context).size.width >= 2500
                        ? FontManager.s18
                        : MediaQuery.of(context).size.width >= 1920
                            ? FontManager.s16
                            : MediaQuery.of(context).size.width >= 1366
                                ? FontManager.s14
                                : FontManager.s12,
                    fontWeight: FontManager.bold,
                    color: Color(0xFF606060),
                  ),
                ),
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        showPopup = false;
                        selectedItem = null;
                        popupType = null;
                        selectedCardKey = null;
                      });
                    },
                    child: Icon(
                      Icons.close,
                      size: AppThemeConstants.iconSizeM,
                      color: Color(0xFF606060),
                    ),
                  ),
                ),
              ],
            ),
            Divider(
              color: Color(0xFFE4E4E4),
              thickness: 1,
            ),

            // Workflow ID section
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Workflow ID',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 12,
                      color: Color(0xFFB3B3B3),
                      height: 1.2,
                      letterSpacing: 0,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '987654321e-76543-2345-2',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF606060),
                      letterSpacing: 0,
                    ),
                  ),
                ],
              ),
            ),

            // Created section
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        'assets/images/dateTime.svg',
                        width: 14,
                        height: 16,
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Created',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 12,
                          color: Color(0xFFB3B3B3),
                          height: 1.2,
                          letterSpacing: 0,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'April 24, 2025',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 13,
                          color: Color(0xFF606060),
                          fontWeight: FontWeight.w600,
                          height: 1.2,
                          letterSpacing: 0,
                        ),
                      ),
                      Text(
                        '11:30 AM',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF606060),
                          letterSpacing: 0,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Updated section
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        'assets/images/dateTime.svg',
                        width: 14,
                        height: 16,
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Updated',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 12,
                          color: Color(0xFFB3B3B3),
                          height: 1.2,
                          letterSpacing: 0,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'April 13, 2025',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 13,
                          color: Color(0xFF606060),
                          fontWeight: FontWeight.w600,
                          height: 1.2,
                          letterSpacing: 0,
                        ),
                      ),
                      Text(
                        '3:30 PM',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF606060),
                          letterSpacing: 0,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: 10),

            Padding(
              padding: const EdgeInsets.only(right: 8, left: 8),
              child: Divider(
                color: Color(0xFFE4E4E4),
                thickness: 1,
              ),
            ),
            // Resume Transaction button
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.only(top: 0, left: 8, right: 8),
                child: Container(
                  width: double.infinity,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(0, 88, 255, 1),
                    borderRadius: BorderRadius.circular(18),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Resume Transaction',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      height: 17 / 14,
                      letterSpacing: 0,
                      color: Color(0xFFFFFFFF),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),

            SizedBox(height: 10),

            // Total Local Objectives section
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                width: double.infinity,
                height: 33,
                decoration: BoxDecoration(
                  color: Color.fromRGBO(0, 88, 255, 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(left: 8),
                child: Text(
                  'Total Local Objectives: 4',
                  style: TextStyle(
                    color: Color(0xFF000000),
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                    letterSpacing: 0,
                  ),
                ),
              ),
            ),

            // Leave Request items
            Padding(
              padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xFFE6F2FF)),
                ),
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Leave Request',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                height: 1.2,
                                letterSpacing: 0,
                                color: Color(0xFF606060),
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'April 04, 2025 | 11:30 AM',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 12,
                                color: Color(0xFF606060),
                                letterSpacing: 0,
                              ),
                            ),
                          ],
                        ),
                      ),
                      MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap: widget.onOpenModal,
                          child: Container(
                            width: 68,
                            height: 20,
                            decoration: BoxDecoration(
                              color: Color.fromRGBO(226, 254, 227, 1),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(13)),
                            ),
                            child: Center(
                              child: Text(
                                'Completed',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: 10,
                                  letterSpacing: 0,
                                  color: Color(0xFF03A015),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            Padding(
              padding: const EdgeInsets.only(top: 0, left: 8, right: 8),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xFFE6F2FF)),
                ),
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Leave Request',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                height: 1.2,
                                letterSpacing: 0,
                                color: Color(0xFF606060),
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'April 04, 2025 | 11:30 AM',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 12,
                                color: Color(0xFF606060),
                                letterSpacing: 0,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: 68,
                        height: 20,
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(226, 254, 227, 1),
                          borderRadius: BorderRadius.all(Radius.circular(13)),
                        ),
                        child: Center(
                          child: Text(
                            'Completed',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 10,
                              letterSpacing: 0,
                              color: Color(0xFF03A015),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            Padding(
              padding: const EdgeInsets.only(top: 0, left: 8, right: 8),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xFFE6F2FF)),
                ),
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Leave Request',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0,
                                color: Color(0xFF606060),
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'April 04, 2025 | 11:30 AM',
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 12,
                                letterSpacing: 0,
                                color: Color(0xFF606060),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: 60,
                        height: 22,
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(238, 227, 202, 1),
                          borderRadius: BorderRadius.all(Radius.circular(13)),
                        ),
                        child: Center(
                          child: Text(
                            'Pending',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              height: 1.2,
                              letterSpacing: 0,
                              color: Color(0xFFCE8F00),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 30),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(String label) {
    final bool isSelected = selectedTab == label;
    return InkWell(
      onTap: () {
        setState(() {
          selectedTab = label;
          applyFilter();
        });
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: FontManager.getCustomStyle(
                fontSize: MediaQuery.of(context).size.width >= 1600
                    ? FontManager.s16
                    : MediaQuery.of(context).size.width >= 1366
                        ? FontManager.s14
                        : FontManager.s12,
                fontFamily: FontManager.fontFamilyInter,
                fontWeight:
                    isSelected ? FontManager.semiBold : FontManager.regular,
                color: isSelected ? Colors.black : Colors.grey,
              ),
            ),
            Container(
              height: 0.5,
              width: 40,
              color: isSelected ? const Color(0xFF0057FF) : Colors.transparent,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaginationControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.filter_alt_outlined, size: 14, color: Colors.grey),
        const SizedBox(width: 5),
        const Text(
          "Filter",
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: 12,
            color: Color(0xFF000000),
          ),
        ),
        const SizedBox(width: 20),

        // Left Arrow with MouseRegion
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: currentPage > 1 ? prevPage : null,
            child: Icon(
              Icons.chevron_left,
              color: currentPage > 1 ? Colors.black : const Color(0xFFD0D0D0),
              size: 18,
            ),
          ),
        ),

        const SizedBox(width: 4),

        Text(
          "$currentPage | $totalPages",
          style: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Color(0xFF242424),
          ),
        ),

        const SizedBox(width: 4),

        // Right Arrow with MouseRegion
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: currentPage < totalPages ? nextPage : null,
            child: Icon(
              Icons.chevron_right,
              color: currentPage < totalPages
                  ? Colors.black
                  : const Color(0xFFD0D0D0),
              size: 18,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChartContent() {
    final ScrollController _scrollController = ScrollController();

    return Scrollbar(
      thumbVisibility: true,
      controller: _scrollController,
      child: SingleChildScrollView(
        controller: _scrollController,
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Calculate available width
            double availableWidth = constraints.maxWidth;
            double screenWidth = MediaQuery.of(context).size.width;

            // Determine cards per row based on screen width
            int cardsPerRow;
            int maxRows = 0; // 0 means no limit

            if (showPopup) {
              if (screenWidth < 1100) {
                cardsPerRow =
                    2; // 2 cards per row for screens < 1100px when popup is open
                maxRows =
                    3; // Maximum 3 rows for screens < 1100px when popup is open
              } else {
                cardsPerRow =
                    3; // Force 3 cards per row when popup is open for larger screens
              }
            } else if (availableWidth >= 1200) {
              cardsPerRow = 4; // 4 cards for 1920px and large screens
            } else if (availableWidth >= 900) {
              cardsPerRow = 3;
            } else if (availableWidth >= 600) {
              cardsPerRow = 2;
            } else {
              cardsPerRow = 1;
            }

            double cardSpacing =
                showPopup ? 20 : 40; // Reduce spacing when popup is open
            double cardWidth =
                (availableWidth - (cardSpacing * (cardsPerRow - 1))) /
                    cardsPerRow;

            // Limit data based on maxRows when applicable
            List<WorkflowItem> dataToShow = paginatedData;
            if (maxRows > 0) {
              int maxItems = cardsPerRow * maxRows;
              if (dataToShow.length > maxItems) {
                dataToShow = dataToShow.sublist(0, maxItems);
              }
            }

            // Build grid layout
            List<Widget> rows = [];
            for (int i = 0; i < dataToShow.length; i += cardsPerRow) {
              List<Widget> rowChildren = [];
              for (int j = 0;
                  j < cardsPerRow && i + j < dataToShow.length;
                  j++) {
                final item = dataToShow[i + j];
                rowChildren.add(
                  GestureDetector(
                    onTap: () {
                      if (item.status == 'Completed' ||
                          item.status == 'Pending') {
                        setState(() {
                          // Single card selection logic
                          String itemKey = "${item.title}_${item.updatedOn}";

                          // If clicking the same card, deselect it
                          if (selectedCardKey == itemKey) {
                            selectedCardKey = null;
                            showPopup = false;
                            selectedItem = null;
                            popupType = null;
                          } else {
                            // Select new card
                            selectedCardKey = itemKey;
                            showPopup = true;
                            selectedItem = item;
                            popupType = item.status;
                          }
                        });
                      }
                    },
                    child: SizedBox(
                      width: cardWidth,
                      child: _workflowBlock(context, item, cardWidth,
                          selectedCardKey == "${item.title}_${item.updatedOn}"),
                    ),
                  ),
                );

                // Add spacing between cards (except for the last card in row)
                if (j < cardsPerRow - 1 && i + j + 1 < dataToShow.length) {
                  rowChildren.add(SizedBox(width: cardSpacing));
                }
              }

              // Fill remaining space if row is not complete
              while (rowChildren.length < (cardsPerRow * 2 - 1)) {
                rowChildren.add(SizedBox(width: cardWidth));
                if (rowChildren.length < (cardsPerRow * 2 - 1)) {
                  rowChildren.add(SizedBox(width: cardSpacing));
                }
              }

              rows.add(
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: rowChildren,
                ),
              );

              // Add vertical spacing between rows with responsive sizing
              if (i + cardsPerRow < dataToShow.length) {
                double screenWidth = MediaQuery.of(context).size.width;
                double bottomSpacing;

                if (screenWidth >= 1920) {
                  bottomSpacing = 30;
                } else if (screenWidth >= 1366) {
                  bottomSpacing = 20;
                } else {
                  bottomSpacing = 21; // Default for smaller screens
                }

                rows.add(SizedBox(height: bottomSpacing));
              }
            }

            // Measure the grid height after build
            WidgetsBinding.instance.addPostFrameCallback((_) {
              final RenderBox? renderBox =
                  _cardGridKey.currentContext?.findRenderObject() as RenderBox?;
              if (renderBox != null) {
                final newHeight = renderBox.size.height;
                if (_cardGridHeight != newHeight) {
                  setState(() {
                    _cardGridHeight = newHeight;
                  });
                }
              }
            });

            return Container(
              key: _cardGridKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: rows,
              ),
            );
          },
        ),
      ),
    );
  }
}

Widget _workflowBlock(BuildContext context, WorkflowItem item, double cardWidth,
    bool isSelected) {
  return MouseRegion(
    cursor: SystemMouseCursors.click,
    child: AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: cardWidth,
      height: MediaQuery.of(context).size.width >= 1600 ? 186 : 133,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          boxShadow: [
            BoxShadow(
              color: Color(0x1A000000),
              offset: Offset(0, 2),
              blurRadius: isSelected ? 8 : 0,
            ),
          ],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFF0057FF) : Colors.transparent,
            width: 1.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 18,
                  height: 18,
                  padding: const EdgeInsets.all(2),
                  child: SvgPicture.asset('assets/images/usermanagement.svg'),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    item.title,
                    style: FontManager.getCustomStyle(
                      fontSize: MediaQuery.of(context).size.width >= 2000
                          ? FontManager.s18
                          : MediaQuery.of(context).size.width >= 1600
                              ? FontManager.s16
                              : MediaQuery.of(context).size.width >= 1366
                                  ? FontManager.s14
                                  : FontManager.s12,
                      fontWeight: FontManager.semiBold,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Color(0xFF606060),
                    ),
                  ),
                ),
              ],
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SvgPicture.asset('assets/images/assignedtome.svg',
                    width: 8, height: 8),
                const SizedBox(width: 4),
                Text(
                  "Assigned to me",
                  style: FontManager.getCustomStyle(
                    fontSize: MediaQuery.of(context).size.width >= 2000
                        ? FontManager.s16
                        : MediaQuery.of(context).size.width >= 1600
                            ? FontManager.s14
                            : FontManager.s12,
                    fontWeight: FontManager.medium,
                    color: const Color(0xFF606060),
                    fontFamily: FontManager.fontFamilyInter,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 5,
            ),
            const Divider(thickness: 1, height: 0, color: Color(0xFFE4E4E4)),
            Padding(
              padding: const EdgeInsets.only(top: 5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Updated on:",
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontSize: MediaQuery.of(context).size.width >= 2000
                              ? FontManager.s14
                              : MediaQuery.of(context).size.width >= 1600
                                  ? FontManager.s12
                                  : FontManager.s10,
                          fontWeight: FontManager.medium,
                          color: Color(0xFFB3B3B3),
                        ),
                      ),
                      Text(
                        item.updatedOn,
                        style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontSize: MediaQuery.of(context).size.width >= 2000
                                ? FontManager.s16
                                : MediaQuery.of(context).size.width >= 1600
                                    ? FontManager.s14
                                    : FontManager.s12,
                            fontWeight: FontManager.semiBold,
                            color: Color(0xFF606060)),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 20,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Color(0xFFE4E4E4)),
                        ),
                        child: Text("1Lo",
                            style: FontManager.getCustomStyle(
                                fontFamily: FontManager.fontFamilyInter,
                                fontSize: MediaQuery.of(context).size.width >=
                                        2000
                                    ? FontManager.s12
                                    : MediaQuery.of(context).size.width >= 1600
                                        ? FontManager.s12
                                        : FontManager.s10,
                                fontWeight: FontManager.medium,
                                color: Colors.blue)),
                      ),
                      const SizedBox(width: 4),
                      Container(
                        width: item.status == "Pending" ? 60 : 68,
                        height: 20,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: item.status == "Pending"
                              ? const Color(0xFFFEF7E2)
                              : const Color(0xFFE0F7E9),
                          borderRadius: BorderRadius.circular(13),
                        ),
                        child: Text(
                          item.status,
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontSize: MediaQuery.of(context).size.width >= 2000
                                ? FontManager.s12
                                : MediaQuery.of(context).size.width >= 1600
                                    ? FontManager.s12
                                    : FontManager.s10,
                            fontWeight: FontManager.medium,
                            color: item.status == "Pending"
                                ? const Color.fromRGBO(206, 143, 0, 1)
                                : const Color.fromRGBO(0, 125, 80, 1),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
