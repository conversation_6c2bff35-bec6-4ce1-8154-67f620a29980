import 'package:flutter/material.dart';

class AppColors {
  // Light Theme Colors
  static const Color primaryIndigo = Color(0xFF4F46E5); // Indigo 600
  static const Color primaryIndigoLight = Color(0xFF818CF8); // Indigo 400
  static const Color primaryIndigoDark = Color(0xFF3730A3); // Indigo 800

  static const Color backgroundLight = Color(0xFFF9FAFB); // Gray 50
  static const Color surfaceLight = Colors.white;
  static const Color textPrimaryLight = Color(0xFF1F2937); // Gray 800
  static const Color textSecondaryLight = Color(0xFF6B7280); // Gray 500

  // Dark Theme Colors
  static const Color backgroundDark = Color(0xFF1F2937); // Gray 800 (not black)
  static const Color surfaceDark = Color(0xFF374151); // Gray 700
  static const Color textPrimaryDark = Color(0xFFF9FAFB); // Gray 50
  static const Color textSecondaryDark = Color(0xFFD1D5DB); // Gray 300

  // Subtle Indigo for Dark Theme Highlights
  static const Color subtleIndigoDark =
      Color(0xFF6366F1); // Adjusted Indigo for dark mode
  static const Color subtleIndigoSurface =
      Color(0xFF312E81); // Indigo 900 - for containers

  // Status Colors (same for both themes)
  static const Color success = Color(0xFF10B981); // Emerald 500
  static const Color error = Color(0xFFEF4444); // Red 500
  static const Color warning = Color(0xFFF59E0B); // Amber 500
  static const Color info = Color(0xFF3B82F6); // Blue 500

  static const Color greyBg = Color(0xffF6F6F6);
  static const Color textBlue = Color(0xff0058FF);
  static const Color textBlue2 = Color(0xff003EE6);
  static const Color black = Colors.black;
  static const Color white = Colors.white;

  static const Color primaryBlue = Color(0xff0058FF);
  static const Color darkBlue = Color(0xff3A5098);
  static const Color grey = Color(0xffF1F3F4);
  static const Color green = Colors.green;

  static const Color lightBlue = Color(0xffE9F2F7);
  static const Color darkGreyBorder = Color(0xffB4B4B4);
  static const Color greyBorder = Color(0xffD0D0D0);
  static const Color textGreyColor = Color(0xff6F747D);
}
