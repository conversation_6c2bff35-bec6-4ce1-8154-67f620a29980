import 'package:flutter/material.dart';
import 'package:nsl/models/entities_data.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/models/role_info.dart';
import 'package:nsl/models/solution/solution_session_model.dart';
import 'package:nsl/models/solution/solution_status_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/base_provider.dart';
import '../services/auth_service.dart';
import '../services/chat_api_service.dart';
import '../utils/logger.dart';
import '../models/message_response.dart';
import '../utils/screen_constants.dart';
import '../models/chat_message.dart';
import 'dart:math' as math;

class WebHomeProvider extends BaseProvider {
  // Chat API service
  final ChatApiService _chatApiService = ChatApiService();

  // Auth service for getting user ID
  final AuthService _authService = AuthService();

  // Chat history
  List<Map<String, dynamic>> _chatHistory = [];

  // Chat messages
  final List<ChatMessage> _messages = [];

  // Store conversation ID
  String? _conversationId;

  // Flag to track if this is the first message in a session
  bool _isFirstMessage = true;

  // Store the conversation session ID for the new API
  String? _currentSessionId;
  String? _nodeId;

  // Current screen name for navigation (using string instead of index for reliability)
  String _currentScreenIndex = ScreenConstants.home;

  String _solutionWidgetsSelectedFrom = "";

  // Chat history expansion state
  bool _isChatHistoryExpanded = false;

  // Store the last user message for API calls (including OCR text)
  String _lastUserMessageForApi = '';

  // Default heights for chat field
  // final double _homeScreenChatHeight = 130.0;
  // final double _otherScreenChatHeight = 60.0;

  // Custom heights that can be set
  // double? _customHomeScreenChatHeight;
  // double? _customOtherScreenChatHeight;

  bool _isLoading = false;
  bool _showSidePanel = false;
  bool _hasTextInChatField = false;

  bool _showStatus = false;
  bool _showStatusArtifacts = false;

  // Mobile chat expansion state
  bool _isMobileChatExpanded = false;

  // OCR panel state
  bool _showOcrPanel = false;
  String _ocrText = '';
  String _ocrFileName = '';

  // Quick message selection
  String? _selectedQuickMessage
      //  = "Solution";
      = "NSL";

  // Solution dropdown selection
  String? _selectedSolutionOption;

  // NSL Thinking expansion state - maps message index to expansion state
  final Map<int, bool> _nslThinkingExpanded = {};

  // Entity selection state
  Entity? _selectedEntity;
  int _selectedSectionIndex = 0;

  // Role selection state
  RoleInfo? _selectedRole;

  // Workflow selection state
  Map<String, dynamic>? _selectedWorkflow;

  // Circuit board tab selection
  String _selectedCircuitTab = 'Synthetic';

  // Getter for conversation ID
  String? get conversationId => _conversationId;

  // Getter for first message flag
  bool get isFirstMessage => _isFirstMessage;

  // Getter for current session ID
  String? get currentSessionId => _currentSessionId;
  String? get nodeId => _nodeId;

  // Getter for current screen name
  String get currentScreenIndex => _currentScreenIndex;
  String get solutionWidgetsSelectedFrom => _solutionWidgetsSelectedFrom;

  // Getter for chat history expanded state
  bool get isChatHistoryExpanded => _isChatHistoryExpanded;

  // Chat UI state getters and setters
  List<ChatMessage> get messages => _messages;

  @override
  bool get isLoading => _isLoading;

  bool get showSidePanel => _showSidePanel;
  bool get hasTextInChatField => _hasTextInChatField;

  bool get showStatus => _showStatus;
  bool get showStatusArtifacts => _showStatusArtifacts;

  // Mobile chat expansion state getter and setter
  bool get isMobileChatExpanded => _isMobileChatExpanded;

  // OCR panel state getters and setters
  bool get showOcrPanel => _showOcrPanel;
  String get ocrText => _ocrText;
  String get ocrFileName => _ocrFileName;

  // Quick message selection getter and setter
  String? get selectedQuickMessage => _selectedQuickMessage;

  // Solution dropdown selection getter and setter
  String? get selectedSolutionOption => _selectedSolutionOption;

  // NSL Thinking expansion state getter
  Map<int, bool> get nslThinkingExpanded => _nslThinkingExpanded;

  // Entity selection state getters and setters
  Entity? get selectedEntity => _selectedEntity;
  int get selectedSectionIndex => _selectedSectionIndex;

  // Role selection state getter and setter
  RoleInfo? get selectedRole => _selectedRole;

  // Workflow selection state getter and setter
  Map<String, dynamic>? get selectedWorkflow => _selectedWorkflow;

  // Circuit board tab selection getter and setter
  String get selectedCircuitTab => _selectedCircuitTab;

  TextEditingController chatController = TextEditingController();

  String selectedSolutionName = "";

  SolutionSessionModel? solutionSessionModel;
  SolutionSessionModel? selectedSolutionSessionModel;

  SolutionStatusModel? solutionStatusModel;

  // Last user message for API calls getter and setter
  String get lastUserMessageForApi => _lastUserMessageForApi;
  set lastUserMessageForApi(String message) {
    _lastUserMessageForApi = message;
    Logger.info('Set lastUserMessageForApi: $message');
  }

  // Add a message to the messages list
  void addMessage(ChatMessage message) {
    _messages.add(message);
    notifyListeners();
  }

  // Replace a message at a specific index
  void replaceMessageAt(int index, ChatMessage message) {
    if (index >= 0 && index < _messages.length) {
      _messages[index] = message;
      notifyListeners();
    }
  }

  // Clear all messages
  void clearMessages() {
    _messages.clear();
    notifyListeners();
  }

  // Set loading state
  set isLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set side panel visibility
  set showSidePanel(bool show) {
    _showSidePanel = show;
    notifyListeners();
  }

  // Set text in chat field state
  set hasTextInChatField(bool hasText) {
    _hasTextInChatField = hasText;
    notifyListeners();
  }

  // Set mobile chat expansion state
  set isMobileChatExpanded(bool expanded) {
    _isMobileChatExpanded = expanded;
    notifyListeners();
  }

  // Toggle mobile chat expansion state
  void toggleMobileChatExpansion() {
    _isMobileChatExpanded = !_isMobileChatExpanded;
    notifyListeners();
  }

  // Set OCR panel visibility
  set showOcrPanel(bool show) {
    _showOcrPanel = show;
    notifyListeners();
  }

  // Clear selected quick message
  void clearQuickMessage() {
    _selectedQuickMessage = '';
    notifyListeners();
  }

  // Set OCR text
  set ocrText(String text) {
    _ocrText = text;
    notifyListeners();
  }

  // Set OCR file name
  set ocrFileName(String name) {
    _ocrFileName = name;
    notifyListeners();
  }

  // Set selected quick message
  set selectedQuickMessage(String? message) {
    _selectedQuickMessage = message;
    notifyListeners();
  }

  // Set selected solution option
  set selectedSolutionOption(String? option) {
    _selectedSolutionOption = option;
    notifyListeners();
  }

  // Set NSL thinking expanded state for a specific message
  void setNslThinkingExpanded(int index, bool expanded) {
    _nslThinkingExpanded[index] = expanded;
    notifyListeners();
  }

  // Clear all NSL thinking expanded states
  void clearNslThinkingExpanded() {
    _nslThinkingExpanded.clear();
    notifyListeners();
  }

  // Set selected entity
  set selectedEntity(Entity? entity) {
    _selectedEntity = entity;
    notifyListeners();
  }

  // Set selected section index
  set selectedSectionIndex(int index) {
    _selectedSectionIndex = index;
    notifyListeners();
  }

  // Set selected role
  set selectedRole(RoleInfo? role) {
    _selectedRole = role;
    notifyListeners();
  }

  // Set selected workflow
  set selectedWorkflow(Map<String, dynamic>? workflow) {
    _selectedWorkflow = workflow;
    notifyListeners();
  }

  // Set selected circuit board tab
  set selectedCircuitTab(String tab) {
    _selectedCircuitTab = tab;
    notifyListeners();
  }

  // Setter for current session ID
  set currentSessionId(String? sessionId) {
    _currentSessionId = sessionId;
    notifyListeners();
  }

  // Setter for current screen name
  set currentScreenIndex(String screenName) {
    // Only update if the value is different and not empty
    if (_currentScreenIndex != screenName && screenName.isNotEmpty) {
      _currentScreenIndex = screenName;
      Logger.info('Set WebHomeProvider currentScreenIndex to $screenName');
      // Save the updated screen name to SharedPreferences
      _saveCurrentScreenIndex();
      notifyListeners();
    } else if (screenName.isEmpty) {
      // If value is empty, log it but don't update or notify
      Logger.info(
          'Attempted to set currentScreenIndex to empty string - ignoring');
    }
  }

  set solutionWidgetsSelectedFrom(String screenName) {
    // Only update if the value is different and not empty
    if (_solutionWidgetsSelectedFrom != screenName) {
      _solutionWidgetsSelectedFrom = screenName;
      Logger.info('Set WebHomeProvider currentScreenIndex to $screenName');
      // Save the updated screen name to SharedPreferences
      _saveSolutionWidgetsIndex();
      notifyListeners();
    } else if (screenName.isEmpty) {
      // If value is empty, log it but don't update or notify
      Logger.info(
          'Attempted to set currentScreenIndex to empty string - ignoring');
    }
  }

  // Save current screen name to SharedPreferences
  Future<void> _saveCurrentScreenIndex() async {
    try {
      // Only save if the current screen index is not empty
      if (_currentScreenIndex.isNotEmpty) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('current_screen_index', _currentScreenIndex);
        Logger.info('Saved current screen index: $_currentScreenIndex');
      } else {
        Logger.info('Not saving empty current screen index');
      }
    } catch (e) {
      Logger.error('Error saving current screen index: $e');
    }
  }

  Future<void> _saveSolutionWidgetsIndex() async {
    try {
      // Only save if the current screen index is not empty
      if (_solutionWidgetsSelectedFrom.isNotEmpty) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(
            'solution_widgets_index', _solutionWidgetsSelectedFrom);
        Logger.info(
            'Saved solution screen index: $_solutionWidgetsSelectedFrom');
      } else {
        Logger.info('Not saving empty current screen index');
      }
    } catch (e) {
      Logger.error('Error saving current screen index: $e');
    }
  }

  // Setter for chat history expanded state
  set isChatHistoryExpanded(bool expanded) {
    _isChatHistoryExpanded = expanded;
    notifyListeners();
    // Save the updated chat history expanded state to SharedPreferences
    _saveChatHistoryExpandedState();
  }

  // Save chat history expanded state to SharedPreferences
  Future<void> _saveChatHistoryExpandedState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_chat_history_expanded', _isChatHistoryExpanded);
      Logger.info('Saved chat history expanded state: $_isChatHistoryExpanded');
    } catch (e) {
      Logger.error('Error saving chat history expanded state: $e');
    }
  }

  WebHomeProvider() {
    _loadConversationState();
    // // Force set to webMyLibrary to ensure first item is active
    // _currentScreenIndex = ScreenConstants.webMyLibrary;
    // _saveCurrentScreenIndex();
  }

  // Load conversation state from SharedPreferences
  Future<void> _loadConversationState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _conversationId = prefs.getString('general_conversation_id');
      _isFirstMessage = prefs.getBool('is_first_message') ?? true;
      _currentSessionId = prefs.getString('current_session_id');

      // Handle migration from int to string for screen index
      if (prefs.containsKey('current_screen_index')) {
        if (prefs.getInt('current_screen_index') != null) {
          // Convert old int value to string name
          final int oldIndex = prefs.getInt('current_screen_index')!;
          switch (oldIndex) {
            case 0:
              _currentScreenIndex = ScreenConstants.home;
              break;
            case 1:
              _currentScreenIndex = ScreenConstants.create;
              break;
            case 6:
              _currentScreenIndex = ScreenConstants.nslJava;
              break;
            default:
              _currentScreenIndex = ScreenConstants.home;
          }
          // Save the new string value
          await prefs.setString('current_screen_index', _currentScreenIndex);
          // Remove the old int value
          await prefs.remove('current_screen_index_int');
        } else {
          // Get the string value
          _currentScreenIndex =
              prefs.getString('current_screen_index') ?? ScreenConstants.home;
        }
      } else {
        _currentScreenIndex = ScreenConstants.home;
      }

      _isChatHistoryExpanded =
          prefs.getBool('is_chat_history_expanded') ?? false;

      Logger.info(
          'Loaded conversation state: ID=$_conversationId, isFirstMessage=$_isFirstMessage, sessionId=$_currentSessionId, screenName=$_currentScreenIndex, isChatHistoryExpanded=$_isChatHistoryExpanded');
    } catch (e) {
      Logger.error('Error loading conversation state: $e');
      _isFirstMessage = true;
      _conversationId = null;
      _currentSessionId = null;
      _currentScreenIndex = ScreenConstants.home;
      _isChatHistoryExpanded = false;
    }
  }

  // Save conversation state to SharedPreferences
  Future<void> _saveConversationState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (_conversationId != null) {
        await prefs.setString('general_conversation_id', _conversationId!);
      }

      if (_currentSessionId != null) {
        await prefs.setString('current_session_id', _currentSessionId!);
      }

      if (_nodeId != null) {
        await prefs.setString('current_node_id', _nodeId!);
      }

      await prefs.setBool('is_first_message', _isFirstMessage);
      await prefs.setString('current_screen_index', _currentScreenIndex);
      await prefs.setBool('is_chat_history_expanded', _isChatHistoryExpanded);

      Logger.info(
          'Saved conversation state: ID=$_conversationId, isFirstMessage=$_isFirstMessage, sessionId=$_currentSessionId, screenName=$_currentScreenIndex, isChatHistoryExpanded=$_isChatHistoryExpanded');
    } catch (e) {
      Logger.error('Error saving conversation state: $e');
    }
  }

  // Reset conversation state
  Future<void> resetConversation({bool preserveScreenIndex = true}) async {
    try {
      // Clear the chat input field
      chatController.clear();
      _conversationId = null;
      _isFirstMessage = true;
      _currentSessionId = null;
      _showStatus = false;
      _showStatusArtifacts = false;
      _isLoading = false;
      // messages.clear();
      resetUIState(preserveScreenIndex: preserveScreenIndex);
      // We don't reset currentScreenIndex here as it's a navigation state

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('general_conversation_id');
      await prefs.remove('current_session_id');
      await prefs.setBool('is_first_message', true);
      await prefs.remove('current_session_id');
      await prefs.remove('current_node_id');
      // Keep the current screen name in SharedPreferences

      Logger.info('Reset conversation state');
      notifyListeners();
    } catch (e) {
      Logger.error('Error resetting conversation state: $e');
    }
  }

  // Reset UI state
  void resetUIState({bool preserveScreenIndex = true}) {
    // Clear messages
    _messages.clear();

    // Reset chat UI state
    _isLoading = false;
    _showSidePanel = false;
    _hasTextInChatField = false;

    // Reset mobile chat expansion state
    _isMobileChatExpanded = false;

    // Reset OCR panel state
    _showOcrPanel = false;
    _ocrText = '';
    _ocrFileName = '';

    // Reset quick message selection
    _selectedQuickMessage = "NSL";

    // Reset solution dropdown selection
    _selectedSolutionOption = null;

    // Clear NSL thinking expanded states
    _nslThinkingExpanded.clear();

    // Reset entity and role selection
    _selectedEntity = null;
    _selectedRole = null;
    _selectedWorkflow = null;
    _selectedSectionIndex = 0;

    // Reset circuit board tab
    _selectedCircuitTab = 'Synthetic';

    // Only set the current screen index to home if not preserving it
    if (!preserveScreenIndex) {
      _currentScreenIndex = ScreenConstants.home;
      // Save the updated screen name to SharedPreferences
      _saveCurrentScreenIndex();
    }

    // Notify listeners of the changes
    // notifyListeners();
  }

  // Helper method to create a title from the first message
  String _createTitleFromMessage(String message) {
    String title = message.trim();
    if (title.length > 50) {
      title = '${title.substring(0, 47)}...';
    }
    return title;
  }

  // Process the response and extract reasoning data if available
  Map<String, dynamic> _processApiResponse(Map<String, dynamic> response) {
    if (!response['success']) {
      return response;
    }

    try {
      // Try to parse the response data as a MessageResponse
      if (response['data'] != null) {
        final data = response['data'];

        // Create a MessageResponse object from the data
        final messageResponse = MessageResponse.fromJson(data);

        // Extract the reasoning data from the MessageResponse
        if (messageResponse.reasoning.isNotEmpty) {
          // Extract just the content from each Reasoning object
          List<String> reasoningContents = messageResponse.reasoning
              .map((reasoning) => reasoning.content)
              .toList();

          // Add the reasoning contents to the response
          response['reasoning'] = reasoningContents;
        }
      }
    } catch (e) {
      // If parsing as MessageResponse fails, fall back to the old method
      Logger.error('Error parsing MessageResponse: $e');

      // Extract reasoning data if available using the old method
      List<String>? reasoningList;
      if (response['data'] != null) {
        final data = response['data'];

        // Check for reasoning in different possible formats
        if (data['reasoning'] != null) {
          var reasoning = data['reasoning'];

          // Convert reasoning to List<String> regardless of its original format
          if (reasoning is List) {
            reasoningList = reasoning.map((item) {
              if (item is Map && item.containsKey('content')) {
                return item['content'].toString();
              }
              return item.toString();
            }).toList();
          } else if (reasoning is String) {
            reasoningList = [reasoning];
          }
        }
      }

      // Add reasoning to the response
      if (reasoningList != null) {
        response['reasoning'] = reasoningList;
      }
    }

    return response;
  }

  // Create a new conversation
  Future<Map<String, dynamic>> createConversation(
      [String? userId, String? title]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    // Use a default title if not provided
    String actualTitle = title ?? 'New Conversation';

    Logger.info(
        'Creating conversation for user ID: $actualUserId with title: $actualTitle');

    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to create a conversation
            final response = await _chatApiService.createConversation(
                actualUserId, actualTitle);

            if (response['success']) {
              // Extract conversation ID from response
              final conversationId = response['conversation_id'];

              if (conversationId != null) {
                // Update state
                _conversationId = conversationId;
                _isFirstMessage = false;
                await _saveConversationState();

                Logger.info(
                    'Created new conversation with ID: $_conversationId');
              }
            }

            return response;
          },
          context: 'WebHomeProvider.createConversation',
        ) ??
        {'success': false, 'message': 'Failed to create conversation'};
  }

  // Send a general question to the API
  Future<Map<String, dynamic>> sendGeneralQuestion(String question,
      [int? userId]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String? actualUserId = userIdStr ?? "1";

    // Use lastUserMessageForApi if available (for OCR text), otherwise use the original question
    String messageToSend =
        _lastUserMessageForApi.isNotEmpty ? _lastUserMessageForApi : question;

    // Log the message being sent
    if (_lastUserMessageForApi.isNotEmpty) {
      Logger.info('Using enhanced message with OCR text for API call');
      Logger.info('Original question: $question');
      Logger.info('Enhanced message: $messageToSend');
    }

    Logger.info('Sending general question for user ID: $actualUserId');

    // If this is the first message, create a conversation first
    if (_isFirstMessage) {
      // Use the first message as the title (always use original question for title)
      String title = _createTitleFromMessage(question);

      final createResult = await createConversation(actualUserId, title);

      if (!createResult['success']) {
        return createResult; // Return the error from create conversation
      }

      // Now we have a conversation ID, proceed with sending the question
    }

    // Ensure we have a conversation ID
    if (_conversationId == null) {
      Logger.error('No conversation ID available for sending question');
      return {
        'success': false,
        'message': 'No conversation ID available',
      };
    }

    final result = await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to send the question with the enhanced message
            final response = await _chatApiService.sendGeneralQuestion(
                messageToSend, _conversationId!, actualUserId);

            // Reset lastUserMessageForApi after sending
            _lastUserMessageForApi = '';

            return _processApiResponse(response);
          },
          context: 'WebHomeProvider.sendGeneralQuestion',
        ) ??
        {'success': false, 'message': 'Failed to send question'};

    return result;
  }

  // Send an internet question to the API
  Future<Map<String, dynamic>> sendInternetQuestion(String question,
      [int? userId]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    // Use lastUserMessageForApi if available (for OCR text), otherwise use the original question
    String messageToSend =
        _lastUserMessageForApi.isNotEmpty ? _lastUserMessageForApi : question;

    // Log the message being sent
    if (_lastUserMessageForApi.isNotEmpty) {
      Logger.info('Using enhanced message with OCR text for API call');
      Logger.info('Original question: $question');
      Logger.info('Enhanced message: $messageToSend');
    }

    Logger.info('Sending internet question for user ID: $actualUserId');

    // If this is the first message, create a conversation first
    if (_isFirstMessage) {
      // Use the first message as the title (always use original question for title)
      String title = _createTitleFromMessage(question);

      final createResult = await createConversation(actualUserId, title);

      if (!createResult['success']) {
        return createResult; // Return the error from create conversation
      }

      // Now we have a conversation ID, proceed with sending the question
    }

    // Ensure we have a conversation ID
    if (_conversationId == null) {
      Logger.error(
          'No conversation ID available for sending internet question');
      return {
        'success': false,
        'message': 'No conversation ID available',
      };
    }

    final result = await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to send the question with the enhanced message
            final response = await _chatApiService.sendInternetQuestion(
                messageToSend, _conversationId!, actualUserId);

            // Reset lastUserMessageForApi after sending
            _lastUserMessageForApi = '';

            return _processApiResponse(response);
          },
          context: 'WebHomeProvider.sendInternetQuestion',
        ) ??
        {'success': false, 'message': 'Failed to send internet question'};

    return result;
  }

  // Send an NSL question to the API
  Future<Map<String, dynamic>> sendNslQuestion(String question,
      [int? userId]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    // Use lastUserMessageForApi if available (for OCR text), otherwise use the original question
    String messageToSend =
        _lastUserMessageForApi.isNotEmpty ? _lastUserMessageForApi : question;

    // Log the message being sent
    if (_lastUserMessageForApi.isNotEmpty) {
      Logger.info('Using enhanced message with OCR text for API call');
      Logger.info('Original question: $question');
      Logger.info('Enhanced message: $messageToSend');
    }

    Logger.info('Sending NSL question for user ID: $actualUserId');

    // If this is the first message, create a conversation first
    if (_isFirstMessage) {
      // Use the first message as the title (always use original question for title)
      String title = _createTitleFromMessage(question);

      final createResult = await createConversation(actualUserId, title);

      if (!createResult['success']) {
        return createResult; // Return the error from create conversation
      }

      // Now we have a conversation ID, proceed with sending the question
    }

    // Ensure we have a conversation ID
    if (_conversationId == null) {
      Logger.error('No conversation ID available for sending NSL question');
      return {
        'success': false,
        'message': 'No conversation ID available',
      };
    }

    final result = await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to send the question with the enhanced message
            final response = await _chatApiService.sendNslQuestion(
                messageToSend, _conversationId!, actualUserId);

            // Reset lastUserMessageForApi after sending
            _lastUserMessageForApi = '';

            return _processApiResponse(response);
          },
          context: 'WebHomeProvider.sendNslQuestion',
        ) ??
        {'success': false, 'message': 'Failed to send NSL question'};

    return result;
  }

  // Fetch chat history
  Future<Map<String, dynamic>> fetchChatHistory([int? userId]) async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Get the user ID from AuthService if not provided
            String? userIdStr =
                userId?.toString() ?? await _authService.getUserId();
            String actualUserId = userIdStr ?? "1";

            Logger.info('Fetching chat history for user ID: $actualUserId');

            // Call the service to fetch chat history
            final response =
                await _chatApiService.fetchChatHistory(actualUserId);

            // Update the chat history
            if (response['success'] &&
                response['data']['conversations'] != null) {
              _chatHistory = List<Map<String, dynamic>>.from(
                  response['data']['conversations']);
              notifyListeners();
            }

            return response;
          },
          context: 'WebHomeProvider.fetchChatHistory',
        ) ??
        {'success': false, 'message': 'Failed to fetch chat history'};
  }

  // Getter for chat history
  List<Map<String, dynamic>> get chatHistory => _chatHistory;

  // Get chat history
  Future<List<Map<String, dynamic>>> getChatHistory() async {
    try {
      // Get the user ID from AuthService
      final userId = await _authService.getUserId();

      if (userId == null) {
        Logger.error('User ID is null, cannot fetch chat history');
        return [];
      }

      // Fetch chat history using the existing method
      final response = await fetchChatHistory(int.tryParse(userId));

      if (response['success'] &&
          response['data'] != null &&
          response['data']['conversations'] != null) {
        // Return the chat history data
        return List<Map<String, dynamic>>.from(
            response['data']['conversations'] ?? []);
      } else {
        Logger.error('Error fetching chat history: ${response['message']}');
        return [];
      }
    } catch (e) {
      Logger.error('Exception fetching chat history: $e');
      return [];
    }
  }

  // Fetch chat history and update messages
  Future<void> fetchChatHistoryAndUpdateMessages() async {
    try {
      final chatHistory = await getChatHistory();

      if (chatHistory.isNotEmpty) {
        // Clear existing messages
        _messages.clear();

        // Add messages from chat history
        for (final chat in chatHistory) {
          // Add user message
          _messages.add(ChatMessage(
            content: chat['question'] ?? '',
            isUser: true,
            timestamp: DateTime.parse(
                chat['timestamp'] ?? DateTime.now().toIso8601String()),
          ));

          // Add AI response
          _messages.add(ChatMessage(
            content: chat['answer'] ?? '',
            isUser: false,
            timestamp: DateTime.parse(
                chat['timestamp'] ?? DateTime.now().toIso8601String()),
          ));
        }

        // Notify listeners of the changes
        notifyListeners();
      }
    } catch (e) {
      Logger.error('Error loading chat history: $e');
    }
  }

  // Create a new conversation session
  Future<Map<String, dynamic>> createNewConversationSession() async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to create a new conversation session
            final response =
                await _chatApiService.createNewConversationSession();

            // Store the session ID if successful
            if (response['success'] && response['session_id'] != null) {
              _currentSessionId = response['session_id'];
              Logger.info(
                  'Stored new conversation session ID: $_currentSessionId');

              // Save the session ID to SharedPreferences
              await _saveConversationState();
            }

            return response;
          },
          context: 'WebHomeProvider.createNewConversationSession',
        ) ??
        {
          'success': false,
          'message': 'Failed to create new conversation session'
        };
  }

  // Send user input to conversation API
  Future<Map<String, dynamic>> sendConversationInput(
      String sessionId, String userInput,
      {FileUploadOcrResponse? fileData}) async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to send user input to conversation API
            final response = await _chatApiService.sendConversationInput(
                sessionId, userInput,
                fileData: fileData);

            return response;
          },
          context: 'WebHomeProvider.sendConversationInput',
        ) ??
        {
          'success': false,
          'message': 'Failed to send user input to conversation API'
        };
  }

  // Load a specific conversation
  Future<Map<String, dynamic>> loadConversation(String conversationId,
      {int? userId}) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    Logger.info(
        'Loading conversation ID: $conversationId for user ID: $actualUserId');

    // Update the conversation ID
    _conversationId = conversationId;
    _isFirstMessage = false;

    // Save the conversation ID
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('general_conversation_id', _conversationId!);
      await prefs.setBool('is_first_message', false);
    } catch (e) {
      Logger.error('Error saving conversation state: $e');
    }

    // Fetch the chat history for this conversation
    final historyResult =
        await fetchConversationHistory(conversationId, actualUserId);

    // Notify listeners
    notifyListeners();

    return historyResult;
  }

  // Fetch chat history for a specific conversation
  Future<Map<String, dynamic>> fetchConversationHistory(String conversationId,
      [String? userId]) async {
    // Get the user ID from AuthService if not provided
    String? userIdStr = userId?.toString() ?? await _authService.getUserId();
    String actualUserId = userIdStr ?? "1";

    Logger.info(
        'Fetching conversation history for conversation ID: $conversationId and user ID: $actualUserId');

    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Call the service to fetch conversation history
            final response = await _chatApiService.fetchConversationHistory(
                conversationId, actualUserId);

            return response;
          },
          context: 'WebHomeProvider.fetchConversationHistory',
        ) ??
        {
          'success': false,
          'message': 'Failed to fetch conversation history',
          'conversation_id': conversationId,
        };
  }

  // Create a new solution session for the first message in a session
  Future<Map<String, dynamic>> createSolutionSession(
      String initialInput) async {
    solutionSessionModel = null;
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            // Get tenant_id from user profile via AuthService
            final savedAuthData = await _authService.getSavedAuthData();
            final tenantId = savedAuthData.data?.user?.tenantId ??
                't001'; // Default fallback
            final userId = savedAuthData.data?.user?.id ?? 'user_001';

            // Keep project_id static for now as requested

            final projectId = "proj_${1000000 + math.Random().nextInt(1000)}";

            Logger.info(
                'Creating solution session with tenant_id: $tenantId, project_id: $projectId');

            // Call the service to create solution session
            final response = await _chatApiService.createSolutionSession(
                tenantId, projectId, initialInput, userId);

            // Store the session ID if successful
            if (response['success'] && response['data'] != null) {
              solutionSessionModel =
                  SolutionSessionModel.fromJson(response["data"]);

              if (solutionSessionModel?.sessionId != null &&
                  solutionSessionModel?.nextNodeId != null) {
                _currentSessionId = solutionSessionModel?.sessionId;
                _nodeId = solutionSessionModel?.conversationId;
                Logger.info(
                    'Stored new solution session ID: $_currentSessionId');

                // Save the session ID to SharedPreferences
                await _saveConversationState();
              }
            }

            return response;
          },
          context: 'WebHomeProvider.createSolutionSession',
        ) ??
        {'success': false, 'message': 'Failed to create solution session'};
  }

  // Create a new solution message in a session
  Future<Map<String, dynamic>> createSolutionMessage(String input) async {
    return await runWithLoadingAndErrorHandling<Map<String, dynamic>>(
          () async {
            final savedAuthData = await _authService.getSavedAuthData();
            final userId = savedAuthData.data?.user?.id ?? 'user_001';
            final response = await _chatApiService.createSolutionMessage(
                currentSessionId, input, _nodeId, userId);
            if (response['success'] && response['data'] != null) {
              solutionSessionModel =
                  SolutionSessionModel.fromJson(response["data"]);
              _nodeId = solutionSessionModel?.conversationId;
              Logger.info('Stored new solution session ID: $_currentSessionId');

              // Save the session ID to SharedPreferences
              await _saveConversationState();
            }
            return response;
          },
          context: 'WebHomeProvider.createSolutionMessage',
        ) ??
        {'success': false, 'message': 'Failed to create solution message'};
  }

  // Get solution status
  Future<bool> getSolutionStatus() async {
    return await runWithLoadingAndErrorHandling<bool>(
          () async {
            final savedAuthData = await _authService.getSavedAuthData();
            final userId = savedAuthData.data?.user?.id ?? 'user_001';
            final response = await _chatApiService.getSolutionStatus(
                currentSessionId, userId);
            solutionStatusModel =
                SolutionStatusModel.fromJson(response["data"]);
            _showStatus = true;
            return true;
          },
          context: 'WebHomeProvider.getSolutionStatus',
        ) ??
        false;
  }

  toggleStatus() {
    _showStatus = !_showStatus;
    notifyListeners();
  }

  toggleStatusArtifacts(SolutionSessionModel? brdDocument) {
    selectedSolutionSessionModel = brdDocument;
    _showStatusArtifacts = !_showStatusArtifacts;
    notifyListeners();
  }
}
