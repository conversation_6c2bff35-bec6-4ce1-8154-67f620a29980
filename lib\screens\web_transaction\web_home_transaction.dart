import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/my_business_home_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/hover_button_widget.dart';
import 'package:nsl/services/multimedia_service.dart';
// import 'package:nsl/screens/web_transaction/web_solution_widgets.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class WebHomeTransaction extends StatefulWidget {
  const WebHomeTransaction({super.key});

  @override
  State<WebHomeTransaction> createState() => _WebHomeTransactionState();
}

class _WebHomeTransactionState extends State<WebHomeTransaction> {
  final List<ChatMessage> messages = [];

  @override
  void initState() {
    super.initState();
    // Initialize the provider controllers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MyBusinessHomeProvider>().initializeControllers();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: Row(
        children: [
          // Navigation Sidebar
          // const WebNavigationSidebar(currentScreen: 'transaction'),

          // Main Content
          Expanded(child: homeBody(context)),
        ],
      ),
    );
  }

  Widget homeBody(BuildContext context) {
    final List<ChatMessage> messages = [];
    return Row(
      children: [
        // Main content area (resized when side panel is shown)
        Expanded(
          child: Container(
            height: MediaQuery.of(context).size.height,
            alignment: Alignment.center,
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Different layouts based on whether chat has started
                  if (messages.isEmpty)
                    // Initial centered layout with welcome message and chat field
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Container(),
                        ),
                        Expanded(
                          flex: 3,
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Center(
                                  child: Consumer<AuthProvider>(
                                    builder: (context, authProvider, _) {
                                      // Get the username from the user profile
                                      final String firstName =
                                          authProvider.user?.username ?? '';

                                      // Get the localized greeting text
                                      final String greeting =
                                          AppLocalizations.of(context)
                                              .translate('home.greeting')
                                              .replaceAll(
                                                  '{name}',
                                                  firstName.isNotEmpty
                                                      ? firstName
                                                      : '');

                                      return Text(
                                        greeting,
                                        style: FontManager.getCustomStyle(
                                          fontSize: ResponsiveFontSizes.custom(
                                            context,
                                            mobile: FontManager.s10,
                                            tablet: 34,
                                            desktop: 34,
                                            web: 38,
                                          ),
                                          // fontWeight: FontManager.medium,
                                          fontFamily:
                                              FontManager.fontFamilyTiemposText,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                SizedBox(height: AppSpacing.md),
                                // Chat field in the center
                                ChatField(
                                  isGeneralLoading: false,
                                  isFileLoading: false,
                                  isSpeechLoading: false,
                                  onSendMessage: () {},
                                  onCancelRequest: () {},
                                  onFileSelected: (p0, p1) {},
                                  onToggleRecording: () {},
                                  controller: TextEditingController(),
                                  multimediaService: MultimediaService(),
                                  height: 130,
                                ),
                                // chatFieldTransaction(context, height: 130),
                                SizedBox(height: AppSpacing.xxs),

                                // Action Cards Section using Provider
                                Consumer<MyBusinessHomeProvider>(
                                  builder: (context, provider, child) {
                                    return Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                HoverableSvgIcon(
                                                  assetPath:
                                                      'assets/images/my_business/home/<USER>',
                                                ),
                                                SizedBox(
                                                  width: AppSpacing.xxs,
                                                ),
                                                Text(
                                                  AppLocalizations.of(context)
                                                      .translate(
                                                          'myBusinessHome.favourites'),
                                                  style: FontManager
                                                      .getCustomStyle(
                                                    fontSize:
                                                        ResponsiveFontSizes
                                                            .custom(
                                                      context,
                                                      mobile: 12,
                                                      tablet: 14,
                                                      desktop: 14,
                                                      web: 16,
                                                    ),
                                                    // fontWeight: FontManager.medium,
                                                    fontFamily: FontManager
                                                        .fontFamilyTiemposText,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SearchIconWithPopup(
                                              actionCardsData:
                                                  provider.actionCardsData,
                                              onToggleFavorite:
                                                  provider.toggleFavorite,
                                            ),
                                          ],
                                        ),

                                        // Row of ActionCards
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: AppSpacing.xs),
                                          child:
                                              _buildActionCardsGrid(provider),
                                        ),

                                        SizedBox(height: AppSpacing.md),
                                        // Label widget example

                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            HoverableSvgIcon(
                                              assetPath:
                                                  'assets/images/my_business/home/<USER>',
                                            ),
                                            SizedBox(
                                              width: AppSpacing.xxs,
                                            ),
                                            Text(
                                              AppLocalizations.of(context)
                                                  .translate(
                                                      'myBusinessHome.recentUsedSolutions'),
                                              style: FontManager.getCustomStyle(
                                                fontSize:
                                                    ResponsiveFontSizes.custom(
                                                  context,
                                                  mobile: 12,
                                                  tablet: 14,
                                                  desktop: 14,
                                                  web: 16,
                                                ),
                                                // fontWeight: FontManager.medium,
                                                fontFamily: FontManager
                                                    .fontFamilyTiemposText,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: AppSpacing.xs,
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: AppSpacing.xxs),
                                          child: _buildLabelWidgets(provider),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: Container(),
                        ),
                      ],
                    )
                  else
                    // Card with text and arrow icon using the reusable component
                    Container()
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionCardsGrid(MyBusinessHomeProvider provider) {
    // Get the screen width
    final screenWidth = MediaQuery.of(context).size.width;

    // Filter actionCardsData to show only selected favorites
    final favoriteItems = provider.favoriteItems;

    // Show message if no favorites selected
    if (favoriteItems.isEmpty) {
      return Center(
        child: Text(
          'No favorites selected',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.custom(
              context,
              mobile: 12,
              tablet: 14,
              desktop: 14,
              web: 16,
            ),
            color: Colors.grey,
            // fontWeight: FontManager.medium,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
      );
    }

    // Convert JSON data to widgets
    final List<Widget> actionCards = [];

    for (int i = 0; i < favoriteItems.length; i++) {
      // Add the action card
      actionCards.add(
        ActionCard(
          text: favoriteItems[i]['text'],
          imagePath: favoriteItems[i]['image'],
          iconData: _getIconData(favoriteItems[i]['icon']),
          onTap: () {
            provider.handleActionCardTap(favoriteItems[i]['action']);
          },
        ),
      );

      // Add spacing between cards (except after the last card)
      if (i < favoriteItems.length - 1) {
        actionCards.add(SizedBox(width: AppSpacing.sm));
      }
    }

    // Use the already defined screenWidth variable
    int crossAxisCount = 6; // Default for medium screens

    // Adjust grid items per row based on screen width
    if (screenWidth >= 1920) {
      crossAxisCount = 8; // More items per row for large screens
    } else if (screenWidth >= 1280) {
      crossAxisCount = 6; // 6 cards for screens between 1280-1919px
    } else {
      crossAxisCount = 5; // Fewer items for smaller screens
    }

    // Remove the SizedBox spacers from actionCards list
    final List<Widget> gridItems = [];
    for (int i = 0; i < actionCards.length; i++) {
      // Only add the actual ActionCard widgets, skip the SizedBox spacers
      if (i % 2 == 0) {
        gridItems.add(actionCards[i]);
      }
    }

    return GridView.builder(
      shrinkWrap: true,
      physics:
          NeverScrollableScrollPhysics(), // Disable scrolling within the grid
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16.0, // Increased spacing between items
        mainAxisSpacing: 16.0, // Increased spacing between rows
        childAspectRatio:
            0.75, // Slightly taller than wide for better text display
      ),
      padding: EdgeInsets.zero, // Remove default padding
      itemCount: gridItems.length,
      itemBuilder: (context, index) {
        return gridItems[index];
      },
    );
  }

  Widget _buildLabelWidgets(MyBusinessHomeProvider provider) {
    // Use labelWidgetsData from provider instead of local data
    final labelWidgetsData =
        provider.labelWidgetsData.take(3).toList(); // Take first 3 items

    // Convert JSON data to widgets
    final List<Widget> labelWidgets = [];

    for (int i = 0; i < labelWidgetsData.length; i++) {
      // Add the label widget
      labelWidgets.add(
        LabelWidget(
          text: labelWidgetsData[i]['text'],
          onTap: () {
            provider.handleLabelWidgetTap(labelWidgetsData[i]['action']);
          },
        ),
      );

      // Add spacing between labels (except after the last label)
      if (i < labelWidgetsData.length - 1) {
        labelWidgets.add(SizedBox(width: AppSpacing.sm));
      }
    }

    return Row(
      children: labelWidgets,
    );
  }
}

// Helper function to convert string icon names to IconData
IconData _getIconData(String iconName) {
  switch (iconName) {
    case 'shopping_cart':
      return Icons.shopping_cart;
    case 'people':
      return Icons.people;
    case 'business':
      return Icons.business;
    case 'event_note':
      return Icons.event_note;
    case 'assessment':
      return Icons.assessment;
    case 'school':
      return Icons.school;
    default:
      return Icons.image; // Default icon
  }
}

Widget chatFieldTransaction(BuildContext context,
    {double? width, double? height}) {
  bool isLoading = false;
  final TextEditingController chatController = TextEditingController();
  // Use ValueNotifier to track text field state
  final ValueNotifier<bool> hasTextInChatField = ValueNotifier<bool>(false);

  // Listen for changes in the text field
  chatController.addListener(() {
    final bool hasText = chatController.text.isNotEmpty;
    if (hasText != hasTextInChatField.value) {
      hasTextInChatField.value = hasText;
    }
  });
  return Container(
    margin: EdgeInsets.symmetric(
      vertical: AppSpacing.sm,
    ),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(AppSpacing.md),
      border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      boxShadow: [
        BoxShadow(
          color: Color(0xffD0D0D0).withValues(alpha: 0.14),
          blurRadius: 20,
          offset: Offset(0, 3),
        ),
      ],
    ),
    constraints: BoxConstraints(
      maxHeight: 400,
      maxWidth: MediaQuery.of(context).size.width / 1.5,
      minHeight: height ?? 130,
    ),
    padding: EdgeInsets.only(
        top: AppSpacing.xxs, left: AppSpacing.xxs, right: AppSpacing.xxs),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: TextField(
            controller: chatController,
            maxLines: 1,
            enabled: !isLoading,
            decoration: InputDecoration(
              focusColor: Colors.transparent,
              hintStyle: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.custom(
                  context,
                  mobile: 12,
                  tablet: 14,
                  desktop: 14,
                  web: 16,
                ),
                color: Colors.grey,
                // fontWeight: FontManager.medium,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              hintText: AppLocalizations.of(context).translate('home.askNSL'),
              hoverColor: Colors.transparent,
              border: OutlineInputBorder(borderSide: BorderSide.none),
              enabledBorder: OutlineInputBorder(borderSide: BorderSide.none),
              focusedBorder: OutlineInputBorder(borderSide: BorderSide.none),
              errorBorder: OutlineInputBorder(borderSide: BorderSide.none),
              disabledBorder: OutlineInputBorder(borderSide: BorderSide.none),
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Add button is always shown
            hoverButtons(icon: Icon(Icons.add), onPressed: () {}),

            // Show either mic button or arrow button based on text state
            ValueListenableBuilder<bool>(
              valueListenable: hasTextInChatField,
              builder: (context, hasText, child) {
                return hasText
                    // Show arrow button when there's text
                    ? hoverButtons(
                        icon: Icon(Icons.arrow_upward),
                        onPressed: () {
                          debugPrint('Send message: ${chatController.text}');
                        },
                      )
                    // Show mic button when there's no text
                    : hoverButtons(
                        icon: Icon(Icons.mic_none), onPressed: () {});
              },
            ),
          ],
        )
      ],
    ),
  );
}

Widget hoverButtons({icon, onPressed}) {
  return HoverButton(
    icon: icon,
    onPressed: onPressed,
  );
}

class ActionCard extends StatefulWidget {
  final String text;
  final String? imagePath;
  final IconData? iconData;
  final VoidCallback? onTap;
  final double? elevation;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final Color? iconColor;
  final double? iconSize;

  const ActionCard({
    super.key,
    required this.text,
    this.imagePath,
    this.iconData,
    this.onTap,
    this.elevation = 1,
    this.padding = const EdgeInsets.all(10.0),
    this.textStyle,
    this.iconColor = Colors.grey,
    this.iconSize = 24,
  });

  @override
  State<ActionCard> createState() => _ActionCardState();
}

class _ActionCardState extends State<ActionCard> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    final defaultTextStyle = FontManager.getCustomStyle(
      fontSize: ResponsiveFontSizes.custom(
        context,
        mobile: 10,
        tablet: 12,
        desktop: 12,
        web: 14,
      ),
      color: Colors.black,
      // fontWeight: FontManager.medium,
      fontFamily: FontManager.fontFamilyTiemposText,
    );

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          final provider = Provider.of<WebHomeProvider>(context, listen: false);
          provider.currentScreenIndex = ScreenConstants.webSolutionWidgets;
          provider.solutionWidgetsSelectedFrom = ScreenConstants.myBusinessHome;
          provider.selectedSolutionName = widget.text;

          widget.onTap!();
        },
        child: Card(
          margin: EdgeInsets.zero,
          elevation: 0,
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
            side: BorderSide(
              color: isHovered
                  ? Color(0xff0058FF)
                  : Color(0xffD0D0D0), // Hover color change
              width: isHovered ? 1 : 0.5, // Thicker border for hover
            ),
          ),
          child: Container(
            width: 140,
            padding: const EdgeInsets.all(10.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image/Icon
                Expanded(
                  flex: 3,
                  child: SizedBox(
                    width: double.infinity,
                    child: widget.imagePath != null
                        ? Image.asset(
                            widget.imagePath!,
                            fit: BoxFit.cover,
                          )
                        : Icon(
                            widget.iconData ?? Icons.image,
                            size: widget.iconSize,
                            color: isHovered
                                ? const Color(0xff0058FF)
                                : widget.iconColor,
                          ),
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                // Text
                Expanded(
                  flex: 2,
                  child: Text(
                    widget.text,
                    style: widget.textStyle ?? defaultTextStyle,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class LabelWidget extends StatefulWidget {
  final String text;
  final Color? backgroundColor;
  final Color? textColor;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;

  const LabelWidget({
    super.key,
    required this.text,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black,
    this.fontSize = 14,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    this.onTap,
  });

  @override
  State<LabelWidget> createState() => _LabelWidgetState();
}

class _LabelWidgetState extends State<LabelWidget> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: Card(
          margin: EdgeInsets.zero,
          elevation: 0,
          color: widget.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(2),
            side: BorderSide(
              color: isHovered
                  ? Color(0xff0058FF)
                  : Color(0xffD0D0D0), // Hover color change
              width: isHovered ? 1 : 0.5, // Thicker border for hover
            ),
          ),
          child: Container(
            padding: widget.padding,
            child: Text(
              widget.text,
              style: FontManager.getCustomStyle(
                color: widget.textColor,
                fontSize: ResponsiveFontSizes.custom(
                  context,
                  mobile: (widget.fontSize ?? 14) * 0.85,
                  tablet: widget.fontSize ?? 14,
                  desktop: widget.fontSize ?? 14,
                  web: (widget.fontSize ?? 14) * 1.15,
                ),
                fontWeight: FontManager.regular,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class SearchIconWithPopup extends StatefulWidget {
  final List<Map<String, dynamic>> actionCardsData;
  final Function(int) onToggleFavorite;

  const SearchIconWithPopup({
    super.key,
    required this.actionCardsData,
    required this.onToggleFavorite,
  });

  @override
  State<SearchIconWithPopup> createState() => _SearchIconWithPopupState();
}

class _SearchIconWithPopupState extends State<SearchIconWithPopup> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  bool _isHovered = false;
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();

  List<Map<String, dynamic>> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _filteredItems = List.from(widget.actionCardsData);
    _searchController.addListener(_filterMenuItems);
  }

  void _filterMenuItems() {
    setState(() {
      final query = _searchController.text.toLowerCase();
      _filteredItems = widget.actionCardsData
          .where((item) => item['text'].toLowerCase().contains(query))
          .toList();
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _isOpen = false;
    _searchController.clear();
    _filteredItems = List.from(widget.actionCardsData);
    setState(() {});
  }

  void _toggleOverlay() {
    if (_isOpen) {
      _removeOverlay();
    } else {
      _showOverlay();
    }
  }

  void _showOverlay() {
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    _isOpen = true;
    setState(() {});
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx - 250,
        top: offset.dy,
        width: 250,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(-250, 0),
          child: StatefulBuilder(
            builder: (context, setState) {
              return MouseRegion(
                onExit: (_) => _removeOverlay(),
                child: Material(
                  elevation: 0,
                  borderRadius: BorderRadius.circular(4),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Color(0xFFE0E0E0), width: 1),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 8.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _searchController,
                                  focusNode: _focusNode,
                                  decoration: InputDecoration(
                                    hintText: 'Search',
                                    contentPadding: EdgeInsets.zero,
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    isDense: true,
                                    hoverColor: Colors.transparent,
                                    hintStyle: FontManager.getCustomStyle(
                                      color: Color(0xffD0D0D0),
                                      fontSize: FontManager.s14,
                                      fontWeight: FontManager.regular,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                    ),
                                  ),
                                  style: FontManager.getCustomStyle(
                                    fontSize: FontManager.s14,
                                    fontWeight: FontManager.regular,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                              SvgPicture.asset(
                                'assets/images/my_business/search_collection.svg',
                                width: 16,
                                height: 16,
                                colorFilter: ColorFilter.mode(
                                    Colors.black, BlendMode.srcIn),
                              ),
                            ],
                          ),
                        ),
                        // Padding(
                        //   padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        //   child: Divider(
                        //       height: 1,
                        //       thickness: 1,
                        //       color: Color(0xFFE0E0E0)),
                        // ),
                        ..._filteredItems.asMap().entries.map((entry) {
                          final index = entry.key;
                          final item = entry.value;
                          final originalIndex =
                              widget.actionCardsData.indexOf(item);
                          return PopupMenuItem(
                            text: item['text'],
                            icon: _getIconData(item['icon']),
                            isSelected: item['isSelected'] ?? false,
                            onToggleFavorite: () {
                              setState(() {
                                widget.onToggleFavorite(originalIndex);
                              });
                            },
                            onTap: () {
                              debugPrint('${item['action']} tapped');
                              _removeOverlay();
                            },
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _removeOverlay();
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Container(
        padding: EdgeInsets.only(right: 4),
        child: MouseRegion(
          onEnter: (_) => setState(() => _isHovered = true),
          onExit: (_) => setState(() => _isHovered = false),
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: _toggleOverlay,
            child: SvgPicture.asset(
              'assets/images/my_business/home/<USER>',
              width: 16,
              height: 16,
              colorFilter: ColorFilter.mode(
                  (_isHovered || _isOpen) ? Color(0xff0058FF) : Colors.black,
                  BlendMode.srcIn),
            ),
          ),
        ),
      ),
    );
  }
}

class PopupMenuItem extends StatefulWidget {
  final String text;
  final IconData icon;
  final VoidCallback onTap;
  final VoidCallback onToggleFavorite;
  final bool isSelected;

  const PopupMenuItem({
    super.key,
    required this.text,
    required this.icon,
    required this.onTap,
    required this.onToggleFavorite,
    this.isSelected = false,
  });

  @override
  State<PopupMenuItem> createState() => _PopupMenuItemState();
}

class _PopupMenuItemState extends State<PopupMenuItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
          color:
              isHovered || widget.isSelected ? Color(0xFFF5F8FF) : Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  widget.text,
                  overflow: TextOverflow.ellipsis,
                  softWrap: false,
                  maxLines: 1,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontManager.regular,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              ),
              GestureDetector(
                onTap: widget.onToggleFavorite,
                child: SvgPicture.asset(
                  'assets/images/my_business/home/<USER>',
                  width: 16,
                  height: 16,
                  colorFilter: ColorFilter.mode(
                    widget.isSelected
                        ? Color(0xff0058FF)
                        : (isHovered
                            ? Color(0xff0058FF)
                            : Colors.grey.shade400),
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HoverableSvgIcon extends StatefulWidget {
  final String assetPath;

  const HoverableSvgIcon({super.key, required this.assetPath});

  @override
  _HoverableSvgIconState createState() => _HoverableSvgIconState();
}

class _HoverableSvgIconState extends State<HoverableSvgIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: SvgPicture.asset(
        widget.assetPath,
        width: 16,
        height: 16,
        colorFilter: ColorFilter.mode(
          isHovered ? Color(0xff0055FF) : Colors.black,
          BlendMode.srcIn,
        ),
      ),
    );
  }
}
