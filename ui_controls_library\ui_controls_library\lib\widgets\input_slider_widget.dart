import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/callback_interpreter.dart';

/// A widget that combines a slider with a text input field.
///
/// This widget allows users to select a value either by dragging the slider
/// or by entering a value directly in the text field.
class InputSliderWidget extends StatefulWidget {
  /// The minimum value the slider can have.
  final double min;

  /// The maximum value the slider can have.
  final double max;

  /// The initial value of the slider.
  final double initial;

  /// The number of discrete divisions the slider can have.
  final int? divisions;

  /// The color of the active portion of the slider.
  final Color activeColor;

  /// The color of the inactive portion of the slider.
  final Color? inactiveColor;

  /// The color of the slider thumb.
  final Color? thumbColor;

  /// The height of the slider track.
  final double? trackHeight;

  /// The style of the label that appears when the slider is active.
  final TextStyle? labelStyle;

  /// The style of the text in the input field.
  final TextStyle? inputStyle;

  /// The decoration for the input field.
  final InputDecoration? inputDecoration;

  /// The prefix text to display before the input field.
  final String? prefix;

  /// The suffix text to display after the input field.
  final String? suffix;

  /// The number of decimal places to show in the input field.
  final int decimalPlaces;

  /// Whether to show increment and decrement buttons.
  final bool showButtons;

  /// Whether to show percentage button.
  final bool showPercentageButton;

  /// Whether the slider is tappable.
  final bool isTappable;

  /// The step value for increment and decrement.
  final double stepValue;

  /// The width of the input field.
  final double inputWidth;

  /// The layout direction of the widget.
  final Axis direction;

  /// The spacing between the slider and input field.
  final double spacing;

  /// Whether to show the min and max labels.
  final bool showMinMaxLabels;

  /// The style for the min and max labels.
  final TextStyle? minMaxLabelStyle;

  /// The label text to display above the slider.
  final String? label;

  /// The style for the label text.
  final TextStyle? labelTextStyle;

  /// The alignment of the label text.
  final TextAlign labelAlignment;

  /// The callback that is called when the slider value changes.
  final ValueChanged<double>? onChanged;

  /// The callback that is called when the slider value change ends.
  final ValueChanged<double>? onChangeEnd;

  // Advanced interaction properties
  /// Callback for when the slider is hovered
  final void Function(bool)? onHover;

  /// Callback for when the slider is focused
  final void Function(bool)? onFocus;

  /// Focus node for the slider
  final FocusNode? focusNode;

  /// Whether the slider should autofocus
  final bool autofocus;

  /// Color to use when the slider is hovered
  final Color? hoverColor;

  /// Color to use when the slider is focused
  final Color? focusColor;

  /// Whether to enable feedback when the slider is interacted with
  final bool enableFeedback;

  /// Callback for when the slider is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the slider is long-pressed
  final VoidCallback? onLongPress;

  // Animation properties
  /// Whether to animate the slider when it changes
  final bool hasAnimation;

  /// Duration of the animation
  final Duration animationDuration;

  /// Curve to use for the animation
  final Curve animationCurve;

  // Slider-specific properties
  /// Whether to show tick marks on the slider
  final bool showTicks;

  /// The color of the tick marks
  final Color? tickMarkColor;

  /// The size of the tick marks
  final double tickMarkRadius;

  /// Whether to show value indicator when the slider is active
  final bool showValueIndicator;

  /// The shape of the slider thumb
  final SliderComponentShape? thumbShape;

  /// The shape of the slider track
  final RoundedRectSliderTrackShape? trackShape;

  /// The shape of the slider value indicator
  final SliderComponentShape? valueIndicatorShape;

  /// The text style of the value indicator
  final TextStyle? valueIndicatorTextStyle;

  /// The color of the overlay when the slider is pressed
  final Color? overlayColor;

  /// The radius of the overlay when the slider is pressed
  final double? overlayRadius;

  // Input field-specific properties
  /// Whether to auto-validate the input field
  final bool autoValidate;

  /// The validator function for the input field
  final String? Function(String?)? validator;

  /// The error text to display when the input is invalid
  final String? errorText;

  /// The helper text to display below the input field
  final String? helperText;

  /// The hint text to display when the input field is empty
  final String? hintText;

  /// The label text to display above the input field
  final String? inputLabel;

  /// The style of the input label
  final TextStyle? inputLabelStyle;

  /// The color of the cursor in the input field
  final Color? cursorColor;

  /// The radius of the cursor in the input field
  final double? cursorRadius;

  /// The width of the cursor in the input field
  final double? cursorWidth;

  /// The height of the cursor in the input field
  final double? cursorHeight;

  // Button-specific properties
  /// The color of the increment/decrement buttons
  final Color? buttonColor;

  /// The color of the icons in the increment/decrement buttons
  final Color? buttonIconColor;

  /// The size of the increment/decrement buttons
  final double buttonSize;

  /// The padding of the increment/decrement buttons
  final EdgeInsetsGeometry buttonPadding;

  /// The shape of the increment/decrement buttons
  final OutlinedBorder? buttonShape;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON validation
  final bool useJsonValidation;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Slider-specific JSON configuration
  /// Whether to use JSON slider configuration
  final bool useJsonSliderConfig;

  /// Slider-specific JSON configuration
  final Map<String, dynamic>? sliderConfig;

  /// Creates an input slider widget.
  const InputSliderWidget({
    super.key,
    this.min = 0.0,
    this.max = 100.0,
    this.initial = 0.0,
    this.divisions,
    this.activeColor = const Color(0xFFE91E63), // Pink/red to match thumb
    this.inactiveColor,
    this.thumbColor,
    this.trackHeight,
    this.labelStyle,
    this.inputStyle,
    this.inputDecoration,
    this.prefix,
    this.suffix,
    this.decimalPlaces = 0,
    this.showButtons = true,
    this.showPercentageButton = false,
    this.isTappable = false,
    this.stepValue = 1.0,
    this.inputWidth = 45.0,
    this.direction = Axis.horizontal,
    this.spacing = 10.0,
    this.showMinMaxLabels = false,
    this.minMaxLabelStyle,
    this.label,
    this.labelTextStyle,
    this.labelAlignment = TextAlign.start,
    this.onChanged,
    this.onChangeEnd,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Animation properties
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    // Slider-specific properties
    this.showTicks = false,
    this.tickMarkColor,
    this.tickMarkRadius = 2.0,
    this.showValueIndicator = true,
    this.thumbShape,
    this.trackShape,
    this.valueIndicatorShape,
    this.valueIndicatorTextStyle,
    this.overlayColor,
    this.overlayRadius,
    // Input field-specific properties
    this.autoValidate = false,
    this.validator,
    this.errorText,
    this.helperText,
    this.hintText,
    this.inputLabel,
    this.inputLabelStyle,
    this.cursorColor,
    this.cursorRadius,
    this.cursorWidth,
    this.cursorHeight,
    // Button-specific properties
    this.buttonColor,
    this.buttonIconColor,
    this.buttonSize = 22.0,
    this.buttonPadding = const EdgeInsets.all(0.0),
    this.buttonShape,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Slider-specific JSON configuration
    this.useJsonSliderConfig = false,
    this.sliderConfig,
  });

  /// Creates an InputSliderWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the InputSliderWidget
  /// through a JSON object, making it easy to create widgets from API   es
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "min": 0,
  ///   "max": 100,
  ///   "initial": 50,
  ///   "divisions": 10,
  ///   "activeColor": "#0066cc",
  ///   "label": "Volume"
  /// }
  /// ```
  factory InputSliderWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return EdgeInsets.zero;
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return EdgeInsets.zero;
    }

    // Parse duration
    Duration parseDuration(dynamic durationValue) {
      if (durationValue == null) {
        return const Duration(milliseconds: 300);
      }

      if (durationValue is int) {
        return Duration(milliseconds: durationValue);
      } else if (durationValue is Map<String, dynamic>) {
        final int milliseconds =
            (durationValue['milliseconds'] as num?)?.toInt() ?? 0;
        final int seconds = (durationValue['seconds'] as num?)?.toInt() ?? 0;
        final int minutes = (durationValue['minutes'] as num?)?.toInt() ?? 0;

        return Duration(
          milliseconds: milliseconds,
          seconds: seconds,
          minutes: minutes,
        );
      } else if (durationValue is String) {
        // Parse strings like "300ms", "2s", "1m"
        final RegExp durationRegExp = RegExp(r'(\d+)(ms|s|m)');
        final match = durationRegExp.firstMatch(durationValue);

        if (match != null) {
          final int value = int.parse(match.group(1)!);
          final String unit = match.group(2)!;

          switch (unit) {
            case 'ms':
              return Duration(milliseconds: value);
            case 's':
              return Duration(seconds: value);
            case 'm':
              return Duration(minutes: value);
            default:
              return const Duration(milliseconds: 300);
          }
        }
      }

      return const Duration(milliseconds: 300);
    }

    // Parse curve
    Curve parseCurve(dynamic curveValue) {
      if (curveValue == null) return Curves.easeInOut;

      if (curveValue is String) {
        switch (curveValue.toLowerCase()) {
          case 'linear':
            return Curves.linear;
          case 'decelerate':
            return Curves.decelerate;
          case 'ease':
            return Curves.ease;
          case 'easein':
          case 'ease_in':
            return Curves.easeIn;
          case 'easeout':
          case 'ease_out':
            return Curves.easeOut;
          case 'easeinout':
          case 'ease_in_out':
            return Curves.easeInOut;
          case 'elasticin':
          case 'elastic_in':
            return Curves.elasticIn;
          case 'elasticout':
          case 'elastic_out':
            return Curves.elasticOut;
          case 'elasticinout':
          case 'elastic_in_out':
            return Curves.elasticInOut;
          case 'bouncein':
          case 'bounce_in':
            return Curves.bounceIn;
          case 'bounceout':
          case 'bounce_out':
            return Curves.bounceOut;
          case 'bounceinout':
          case 'bounce_in_out':
            return Curves.bounceInOut;
          default:
            return Curves.easeInOut;
        }
      }

      return Curves.easeInOut;
    }

    // Parse text style
    TextStyle? parseTextStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is Map<String, dynamic>) {
        final color = parseColor(styleValue['color']);
        final fontSize =
            styleValue['fontSize'] != null
                ? (styleValue['fontSize'] as num).toDouble()
                : null;
        final fontWeight =
            styleValue['fontWeight'] != null
                ? (styleValue['fontWeight'] == 'bold'
                    ? FontWeight.bold
                    : styleValue['fontWeight'] == 'normal'
                    ? FontWeight.normal
                    : FontWeight.normal)
                : null;
        final fontStyle =
            styleValue['fontStyle'] != null
                ? (styleValue['fontStyle'] == 'italic'
                    ? FontStyle.italic
                    : FontStyle.normal)
                : null;
        final letterSpacing =
            styleValue['letterSpacing'] != null
                ? (styleValue['letterSpacing'] as num).toDouble()
                : null;
        final wordSpacing =
            styleValue['wordSpacing'] != null
                ? (styleValue['wordSpacing'] as num).toDouble()
                : null;
        final height =
            styleValue['height'] != null
                ? (styleValue['height'] as num).toDouble()
                : null;
        final decoration =
            styleValue['decoration'] != null
                ? (styleValue['decoration'] == 'underline'
                    ? TextDecoration.underline
                    : styleValue['decoration'] == 'lineThrough'
                    ? TextDecoration.lineThrough
                    : styleValue['decoration'] == 'overline'
                    ? TextDecoration.overline
                    : null)
                : null;
        final fontFamily = styleValue['fontFamily'] as String?;

        return TextStyle(
          color: color,
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontStyle: fontStyle,
          letterSpacing: letterSpacing,
          wordSpacing: wordSpacing,
          height: height,
          decoration: decoration,
          fontFamily: fontFamily,
        );
      }

      return null;
    }

    // Parse input decoration
    InputDecoration? parseInputDecoration(dynamic decorationValue) {
      if (decorationValue == null) return null;

      if (decorationValue is Map<String, dynamic>) {
        final labelText = decorationValue['labelText'] as String?;
        final hintText = decorationValue['hintText'] as String?;
        final helperText = decorationValue['helperText'] as String?;
        final errorText = decorationValue['errorText'] as String?;
        final prefixText = decorationValue['prefixText'] as String?;
        final suffixText = decorationValue['suffixText'] as String?;
        final labelStyle = parseTextStyle(decorationValue['labelStyle']);
        final hintStyle = parseTextStyle(decorationValue['hintStyle']);
        final helperStyle = parseTextStyle(decorationValue['helperStyle']);
        final errorStyle = parseTextStyle(decorationValue['errorStyle']);
        final prefixStyle = parseTextStyle(decorationValue['prefixStyle']);
        final suffixStyle = parseTextStyle(decorationValue['suffixStyle']);
        final fillColor = parseColor(decorationValue['fillColor']);
        final filled = decorationValue['filled'] as bool?;
        final isDense = decorationValue['isDense'] as bool?;
        final contentPadding = parseEdgeInsets(
          decorationValue['contentPadding'],
        );

        // Parse border
        InputBorder? parseBorder(dynamic borderValue) {
          if (borderValue == null) return null;

          if (borderValue is Map<String, dynamic>) {
            final borderType = borderValue['type'] as String?;
            final borderRadius =
                borderValue['radius'] != null
                    ? (borderValue['radius'] as num).toDouble()
                    : 0.0;
            final borderColor = parseColor(borderValue['color']) ?? Colors.grey;
            final borderWidth =
                borderValue['width'] != null
                    ? (borderValue['width'] as num).toDouble()
                    : 1.0;

            switch (borderType?.toLowerCase()) {
              case 'outline':
                return OutlineInputBorder(
                  borderRadius: BorderRadius.circular(0),
                  borderSide: BorderSide(
                    color: borderColor,
                    width: borderWidth,
                  ),
                );
              case 'underline':
                return UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: borderColor,
                    width: borderWidth,
                  ),
                );
              case 'none':
                return InputBorder.none;
              default:
                return OutlineInputBorder(
                  borderRadius: BorderRadius.circular(borderRadius),
                  borderSide: BorderSide(
                    color: borderColor,
                    width: borderWidth,
                  ),
                );
            }
          }

          return null;
        }

        final border = parseBorder(decorationValue['border']);
        final enabledBorder = parseBorder(decorationValue['enabledBorder']);
        final focusedBorder = parseBorder(decorationValue['focusedBorder']);
        final errorBorder = parseBorder(decorationValue['errorBorder']);
        final focusedErrorBorder = parseBorder(
          decorationValue['focusedErrorBorder'],
        );
        final disabledBorder = parseBorder(decorationValue['disabledBorder']);

        return InputDecoration(
          labelText: labelText,
          hintText: hintText,
          helperText: helperText,
          errorText: errorText,
          prefixText: prefixText,
          suffixText: suffixText,
          labelStyle: labelStyle,
          hintStyle: hintStyle,
          helperStyle: helperStyle,
          errorStyle: errorStyle,
          prefixStyle: prefixStyle,
          suffixStyle: suffixStyle,
          fillColor: fillColor,
          filled: filled,
          isDense: isDense,
          contentPadding: contentPadding as EdgeInsetsGeometry?,
          border: border,
          enabledBorder: enabledBorder,
          focusedBorder: focusedBorder,
          errorBorder: errorBorder,
          focusedErrorBorder: focusedErrorBorder,
          disabledBorder: disabledBorder,
        );
      }

      return null;
    }

    // Parse shape border
    OutlinedBorder? parseShapeBorder(dynamic shapeValue) {
      if (shapeValue == null) return null;

      if (shapeValue is Map<String, dynamic>) {
        final shapeType = shapeValue['type'] as String?;
        final borderRadius =
            shapeValue['radius'] != null
                ? (shapeValue['radius'] as num).toDouble()
                : 0.0;

        switch (shapeType?.toLowerCase()) {
          case 'rounded':
            return RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            );
          case 'circle':
            return const CircleBorder();
          case 'stadium':
            return const StadiumBorder();
          case 'beveled':
            return BeveledRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            );
          default:
            return RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            );
        }
      }

      return null;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onChanged'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onChanged'] = json['onChanged'];
      useJsonCallbacks = true;
    }

    if (json['onChangeEnd'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onChangeEnd'] = json['onChangeEnd'];
      useJsonCallbacks = true;
    }

    // Parse Slider-specific configuration
    Map<String, dynamic>? sliderConfig;
    bool useJsonSliderConfig = json['useJsonSliderConfig'] as bool? ?? false;

    if (json['sliderConfig'] != null) {
      if (json['sliderConfig'] is Map) {
        sliderConfig = Map<String, dynamic>.from(json['sliderConfig'] as Map);
        useJsonSliderConfig = true;
      } else if (json['sliderConfig'] is String) {
        try {
          sliderConfig =
              jsonDecode(json['sliderConfig'] as String)
                  as Map<String, dynamic>;
          useJsonSliderConfig = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return InputSliderWidget(
      // Basic properties
      min: json['min'] != null ? (json['min'] as num).toDouble() : 0.0,
      max: json['max'] != null ? (json['max'] as num).toDouble() : 100.0,
      initial:
          json['initial'] != null ? (json['initial'] as num).toDouble() : 0.0,
      divisions: json['divisions'] as int?,
      activeColor: parseColor(json['activeColor']) ?? const Color(0xFFE91E63),
      inactiveColor: parseColor(json['inactiveColor']),
      thumbColor: parseColor(json['thumbColor']),
      trackHeight:
          json['trackHeight'] != null
              ? (json['trackHeight'] as num).toDouble()
              : null,
      labelStyle: parseTextStyle(json['labelStyle']),
      inputStyle: parseTextStyle(json['inputStyle']),
      inputDecoration: parseInputDecoration(json['inputDecoration']),
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      decimalPlaces: json['decimalPlaces'] as int? ?? 0,
      showButtons: json['showButtons'] as bool? ?? true,
      stepValue:
          json['stepValue'] != null
              ? (json['stepValue'] as num).toDouble()
              : 1.0,
      inputWidth:
          json['inputWidth'] != null
              ? (json['inputWidth'] as num).toDouble()
              : 80.0,
      direction:
          json['direction'] == 'vertical' ? Axis.vertical : Axis.horizontal,
      spacing:
          json['spacing'] != null ? (json['spacing'] as num).toDouble() : 16.0,
      showMinMaxLabels: json['showMinMaxLabels'] as bool? ?? false,
      minMaxLabelStyle: parseTextStyle(json['minMaxLabelStyle']),
      label: json['label'] as String?,
      labelTextStyle: parseTextStyle(json['labelTextStyle']),
      labelAlignment:
          json['labelAlignment'] == 'center'
              ? TextAlign.center
              : json['labelAlignment'] == 'end'
              ? TextAlign.end
              : TextAlign.start,

      // Advanced interaction properties
      onHover: null, // Cannot be created from JSON directly
      onFocus: null, // Cannot be created from JSON directly
      focusNode: null, // Cannot be created from JSON directly
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap: null, // Cannot be created from JSON directly
      onLongPress: null, // Cannot be created from JSON directly
      // Animation properties
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: parseDuration(json['animationDuration']),
      animationCurve: parseCurve(json['animationCurve']),

      // Slider-specific properties
      showTicks: json['showTicks'] as bool? ?? false,
      tickMarkColor: parseColor(json['tickMarkColor']),
      tickMarkRadius:
          json['tickMarkRadius'] != null
              ? (json['tickMarkRadius'] as num).toDouble()
              : 2.0,
      showValueIndicator: json['showValueIndicator'] as bool? ?? true,
      thumbShape: null, // Cannot be created from JSON directly
      trackShape: null, // Cannot be created from JSON directly
      valueIndicatorShape: null, // Cannot be created from JSON directly
      valueIndicatorTextStyle: parseTextStyle(json['valueIndicatorTextStyle']),
      overlayColor: parseColor(json['overlayColor']),
      overlayRadius:
          json['overlayRadius'] != null
              ? (json['overlayRadius'] as num).toDouble()
              : null,

      // Input field-specific properties
      autoValidate: json['autoValidate'] as bool? ?? false,
      validator: null, // Cannot be created from JSON directly
      errorText: json['errorText'] as String?,
      helperText: json['helperText'] as String?,
      hintText: json['hintText'] as String?,
      inputLabel: json['inputLabel'] as String?,
      inputLabelStyle: parseTextStyle(json['inputLabelStyle']),
      cursorColor: parseColor(json['cursorColor']),
      cursorRadius:
          json['cursorRadius'] != null
              ? (json['cursorRadius'] as num).toDouble()
              : null,
      cursorWidth:
          json['cursorWidth'] != null
              ? (json['cursorWidth'] as num).toDouble()
              : null,
      cursorHeight:
          json['cursorHeight'] != null
              ? (json['cursorHeight'] as num).toDouble()
              : null,

      // Button-specific properties
      buttonColor: parseColor(json['buttonColor']),
      buttonIconColor: parseColor(json['buttonIconColor']),
      buttonSize:
          json['buttonSize'] != null
              ? (json['buttonSize'] as num).toDouble()
              : 18.0,
      buttonPadding: parseEdgeInsets(json['buttonPadding']),
      buttonShape: parseShapeBorder(json['buttonShape']),

      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : null,
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,

      // Slider-specific JSON configuration
      useJsonSliderConfig: useJsonSliderConfig,
      sliderConfig: sliderConfig,
    );
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'min': min,
      'max': max,
      'initial': initial,
      'divisions': divisions,
      'activeColor': '#${activeColor.value.toRadixString(16)}',
      'inactiveColor':
          inactiveColor != null
              ? '#${inactiveColor!.value.toRadixString(16)}'
              : null,
      'thumbColor':
          thumbColor != null ? '#${thumbColor!.value.toRadixString(16)}' : null,
      'trackHeight': trackHeight,
      'prefix': prefix,
      'suffix': suffix,
      'decimalPlaces': decimalPlaces,
      'showButtons': showButtons,
      'stepValue': stepValue,
      'inputWidth': inputWidth,
      'direction': direction == Axis.vertical ? 'vertical' : 'horizontal',
      'spacing': spacing,
      'showMinMaxLabels': showMinMaxLabels,
      'label': label,
      'labelAlignment':
          labelAlignment == TextAlign.center
              ? 'center'
              : labelAlignment == TextAlign.end
              ? 'end'
              : 'start',

      // Advanced properties
      'autofocus': autofocus,
      'hoverColor':
          hoverColor != null ? '#${hoverColor!.value.toRadixString(16)}' : null,
      'focusColor':
          focusColor != null ? '#${focusColor!.value.toRadixString(16)}' : null,
      'enableFeedback': enableFeedback,

      // Animation properties
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,

      // Slider-specific properties
      'showTicks': showTicks,
      'tickMarkColor':
          tickMarkColor != null
              ? '#${tickMarkColor!.value.toRadixString(16)}'
              : null,
      'tickMarkRadius': tickMarkRadius,
      'showValueIndicator': showValueIndicator,
      'overlayColor':
          overlayColor != null
              ? '#${overlayColor!.value.toRadixString(16)}'
              : null,
      'overlayRadius': overlayRadius,

      // Input field-specific properties
      'autoValidate': autoValidate,
      'errorText': errorText,
      'helperText': helperText,
      'hintText': hintText,
      'inputLabel': inputLabel,
      'cursorColor':
          cursorColor != null
              ? '#${cursorColor!.value.toRadixString(16)}'
              : null,
      'cursorWidth': cursorWidth,
      'cursorHeight': cursorHeight,

      // Button-specific properties
      'buttonColor':
          buttonColor != null
              ? '#${buttonColor!.value.toRadixString(16)}'
              : null,
      'buttonIconColor':
          buttonIconColor != null
              ? '#${buttonIconColor!.value.toRadixString(16)}'
              : null,
      'buttonSize': buttonSize,

      // JSON configuration
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonSliderConfig': useJsonSliderConfig,
    };
  }

  @override
  InputSliderWidgetState createState() => InputSliderWidgetState();
}

class InputSliderWidgetState extends State<InputSliderWidget>
    with SingleTickerProviderStateMixin {
  late double _currentValue;
  late TextEditingController _controller;
  bool _isEditing = false;

  // Animation controller for the slider
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Callback state
  late Map<String, dynamic> _callbackState;

  // Parsed JSON configuration
  Map<String, dynamic>? _parsedJsonConfig;

  // Validation state
  bool _isValid = true;

  @override
  void initState() {
    super.initState();

    // Initialize current value
    _currentValue = widget.initial.clamp(widget.min, widget.max);

    // Initialize text controller
    _controller = TextEditingController(text: _formatValue(_currentValue));

    // Initialize callback state
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Create animation
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );

    // Start animation if needed
    if (widget.hasAnimation) {
      _animationController.forward();
    }

    // Execute onInit callback if defined in JSON
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onInit')) {
      _executeJsonCallback('onInit');
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['value'] = _currentValue;
      _callbackState['min'] = widget.min;
      _callbackState['max'] = widget.max;
      _callbackState['text'] = _controller.text;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = _currentValue;
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the current value
  ///
  /// This method applies validation rules defined in the JSON configuration.
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply min/max validation
        if (rules.containsKey('min') &&
            _currentValue < (rules['min'] as num).toDouble()) {
          _isValid = false;
          return;
        }

        if (rules.containsKey('max') &&
            _currentValue > (rules['max'] as num).toDouble()) {
          _isValid = false;
          return;
        }

        // Apply step validation
        if (rules.containsKey('step')) {
          final step = (rules['step'] as num).toDouble();
          final remainder = (_currentValue - widget.min) % step;
          if (remainder > 0.0001 && remainder < step - 0.0001) {
            _isValid = false;
            return;
          }
        }
      }
    }

    _isValid = true;
  }

  /// Applies JSON styling to the widget
  ///
  /// This method applies styling rules defined in the JSON configuration.
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  ///
  /// This method applies formatting rules defined in the JSON configuration.
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  String _formatValue(double value) {
    if (widget.decimalPlaces <= 0) {
      return value.round().toString();
    } else {
      return value.toStringAsFixed(widget.decimalPlaces);
    }
  }

  void _updateValueFromSlider(double value) {
    // Execute onBeforeChange callback if defined in JSON
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onBeforeChange')) {
      _executeJsonCallback('onBeforeChange', value);
    }

    setState(() {
      _currentValue = value;
      if (!_isEditing) {
        _controller.text = _formatValue(value);
      }
    });

    // Apply JSON validation if enabled
    if (widget.useJsonValidation) {
      _applyJsonValidation();
    }

    // Call standard callback
    widget.onChanged?.call(value);

    // Execute JSON callback
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onChanged')) {
      _executeJsonCallback('onChanged', value);
    }

    // Animate if needed
    if (widget.hasAnimation) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _updateValueFromTextField(String text) {
    if (text.isEmpty) return;

    double? newValue = double.tryParse(text);
    if (newValue != null) {
      // Execute onBeforeChange callback if defined in JSON
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onBeforeChange')) {
        _executeJsonCallback('onBeforeChange', newValue);
      }

      newValue = newValue.clamp(widget.min, widget.max);
      setState(() {
        _currentValue = newValue!;
      });

      // Apply JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Call standard callback
      widget.onChanged?.call(newValue);

      // Execute JSON callback
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onChanged')) {
        _executeJsonCallback('onChanged', newValue);
      }

      // Animate if needed
      if (widget.hasAnimation) {
        _animationController.reset();
        _animationController.forward();
      }
    }
  }

  void _incrementValue() {
    final newValue = (_currentValue + widget.stepValue).clamp(
      widget.min,
      widget.max,
    );

    // Execute onBeforeChange callback if defined in JSON
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onBeforeChange')) {
      _executeJsonCallback('onBeforeChange', newValue);
    }

    setState(() {
      _currentValue = newValue;
      _controller.text = _formatValue(newValue);
    });

    // Apply JSON validation if enabled
    if (widget.useJsonValidation) {
      _applyJsonValidation();
    }

    // Call standard callback
    widget.onChanged?.call(newValue);

    // Execute JSON callback
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onChanged')) {
      _executeJsonCallback('onChanged', newValue);
    }

    // Execute increment callback if defined in JSON
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onIncrement')) {
      _executeJsonCallback('onIncrement', newValue);
    }

    // Animate if needed
    if (widget.hasAnimation) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _decrementValue() {
    final newValue = (_currentValue - widget.stepValue).clamp(
      widget.min,
      widget.max,
    );

    // Execute onBeforeChange callback if defined in JSON
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onBeforeChange')) {
      _executeJsonCallback('onBeforeChange', newValue);
    }

    setState(() {
      _currentValue = newValue;
      _controller.text = _formatValue(newValue);
    });

    // Apply JSON validation if enabled
    if (widget.useJsonValidation) {
      _applyJsonValidation();
    }

    // Call standard callback
    widget.onChanged?.call(newValue);

    // Execute JSON callback
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onChanged')) {
      _executeJsonCallback('onChanged', newValue);
    }

    // Execute decrement callback if defined in JSON
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onDecrement')) {
      _executeJsonCallback('onDecrement', newValue);
    }

    // Animate if needed
    if (widget.hasAnimation) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  Widget _buildSlider() {
    // Create custom slider theme data matching the basic slider design
    final sliderThemeData = SliderThemeData(
      thumbShape: _CustomInputSliderThumbCircle(
        thumbRadius: 12,
        thumbColor: widget.activeColor,
        min: widget.min,
        max: widget.max,
        textStyle: TextStyle(
          fontSize: 12,
          fontFamily: 'Inter',
          color: widget.activeColor,
          fontWeight: FontWeight.normal,
        ),
      ),
      overlayShape: SliderComponentShape.noOverlay,
      activeTrackColor: widget.activeColor,
      inactiveTrackColor: widget.inactiveColor ?? Colors.grey.shade300,
      thumbColor: Colors.transparent,
      showValueIndicator: ShowValueIndicator.never,
      trackHeight: widget.trackHeight ?? 4.0,
      trackShape: const _CustomSliderTrackShape(),
    );

    // Create the slider
    Widget slider = Padding(
      padding: const EdgeInsets.all(0.0),
      child: SliderTheme(
        data: sliderThemeData,
        child: Slider(
          padding: EdgeInsets.zero,
          min: widget.min,
          max: widget.max,
          value: _currentValue,
          divisions: widget.divisions,
          onChanged: _updateValueFromSlider,
          onChangeEnd: (value) {
            // Execute onChangeEnd callback if defined in JSON
            if (widget.useJsonCallbacks &&
                widget.jsonCallbacks != null &&
                widget.jsonCallbacks!.containsKey('onChangeEnd')) {
              _executeJsonCallback('onChangeEnd', value);
            }

            // Call standard callback
            widget.onChangeEnd?.call(value);
          },
          autofocus: widget.autofocus,
          focusNode: widget.focusNode,
          mouseCursor: widget.isTappable ? SystemMouseCursors.click : null,
        ),
      ),
    );

    // Apply animation if needed
    if (widget.hasAnimation) {
      slider = FadeTransition(opacity: _animation, child: slider);
    }

    // Apply advanced interaction properties
    if (widget.onHover != null ||
        widget.onDoubleTap != null ||
        widget.onLongPress != null) {
      slider = MouseRegion(
        onEnter: widget.onHover != null ? (_) => widget.onHover!(true) : null,
        onExit: widget.onHover != null ? (_) => widget.onHover!(false) : null,
        cursor:
            widget.isTappable ? SystemMouseCursors.click : MouseCursor.defer,
        child: GestureDetector(
          onDoubleTap:
              widget.onDoubleTap != null
                  ? () {
                    // Execute onDoubleTap callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                      _executeJsonCallback('onDoubleTap');
                    }

                    // Call standard callback
                    widget.onDoubleTap!();
                  }
                  : null,
          onLongPress:
              widget.onLongPress != null
                  ? () {
                    // Execute onLongPress callback if defined in JSON
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onLongPress')) {
                      _executeJsonCallback('onLongPress');
                    }

                    // Call standard callback
                    widget.onLongPress!();
                  }
                  : null,
          child: slider,
        ),
      );
    }

    return slider;
  }

  Widget _buildTextField() {
    // Create enhanced input decoration
    final InputDecoration effectiveDecoration =
        widget.inputDecoration ??
        InputDecoration(
          contentPadding: EdgeInsets.symmetric(
            horizontal: 0.0,
            vertical: _getResponsiveVerticalPadding(context),
          ),
          
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4.0),
              borderSide: BorderSide(
                color: Color(0xFFCCCCCC), // Default border color
                width: 1.0,         // Border width
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4.0),
              borderSide: BorderSide(
                color: Colors.blue, // Focused border color
                width: 1.0,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4.0),
              borderSide: BorderSide(
                color: Color(0xFFCCCCCC), // Enabled (but not focused) border
                width: 1.0,
              ),
            
),
 // 
           // // Remove border radius
          prefixText: widget.prefix,
          suffixText: widget.suffix,
          isDense: true,
          labelText: widget.inputLabel,
          labelStyle: widget.inputLabelStyle,
          helperText: widget.helperText,
          hintText: widget.hintText,
          errorText: widget.errorText,
        );

    // Create the text field
    Widget textField = TextField( 
      controller: _controller,
      keyboardType: TextInputType.numberWithOptions(
        decimal: widget.decimalPlaces > 0,
        signed: widget.min < 0,
      ),
      textAlign: TextAlign.center,
      style: widget.inputStyle ?? const TextStyle(fontSize: 30),
      decoration: effectiveDecoration,
      inputFormatters: [
        FilteringTextInputFormatter.allow(
          widget.decimalPlaces > 0
              ? RegExp(
                r'^\-?\d*\.?\d{0,' + widget.decimalPlaces.toString() + r'}$',
              )
              : RegExp(r'^\-?\d*$'),
        ),
      ],
      onChanged: (text) {
        // Execute onTextChange callback if defined in JSON
        if (widget.useJsonCallbacks &&
            widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onTextChange')) {
          _executeJsonCallback('onTextChange', text);
        }

        setState(() {
          _isEditing = true;
        });
      },
      onSubmitted: (text) {
        // Execute onTextSubmit callback if defined in JSON
        if (widget.useJsonCallbacks &&
            widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onTextSubmit')) {
          _executeJsonCallback('onTextSubmit', text);
        }

        _updateValueFromTextField(text);
        setState(() {
          _isEditing = false;
        });
      },
      onEditingComplete: () {
        _updateValueFromTextField(_controller.text);
        setState(() {
          _isEditing = false;
        });
      },
      autofocus: widget.autofocus,
      focusNode: widget.focusNode,
      onTap:
          widget.isTappable
              ? () {
                // Execute onTap callback if defined in JSON
                if (widget.useJsonCallbacks &&
                    widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onInputTap')) {
                  _executeJsonCallback('onInputTap');
                }
              }
              : null,
      cursorColor: widget.cursorColor,
      cursorWidth: widget.cursorWidth ?? 2.0,
      cursorHeight: widget.cursorHeight,
      cursorRadius:
          widget.cursorRadius != null
              ? Radius.circular(widget.cursorRadius!)
              : null,
      enableInteractiveSelection: true,
      enableSuggestions: false,
      autocorrect: false,
    );

    // Apply validation if enabled
    // if (widget.autoValidate && widget.validator != null) {
    //   textField = FormField<String>(
    //     initialValue: _controller.text,
    //     validator: widget.validator,
    //     autovalidateMode: AutovalidateMode.always,
    //     builder: (FormFieldState<String> state) {
    //       return Column(
    //         crossAxisAlignment: CrossAxisAlignment.start,
    //         mainAxisSize: MainAxisSize.min,
    //         children: [
    //           textField,
    //           if (state.hasError)
    //             Padding(
    //               padding: const EdgeInsets.only(top: 4.0, left: 12.0),
    //               child: Text(
    //                 state.errorText!,
    //                 style: TextStyle(
    //                   color: Theme.of(context).colorScheme.error,
    //                   fontSize: 12.0,
    //                 ),
    //               ),
    //             ),
    //         ],
    //       );
    //     },
    //   );
    // }

    // // Apply animation if needed
    // if (widget.hasAnimation) {
    //   textField = FadeTransition(opacity: _animation, child: textField);
    // }

    // // Apply advanced interaction properties
    // if (widget.onHover != null ||
    //     widget.onDoubleTap != null ||
    //     widget.onLongPress != null) {
    //   textField = MouseRegion(
    //     onEnter: widget.onHover != null ? (_) => widget.onHover!(true) : null,
    //     onExit: widget.onHover != null ? (_) => widget.onHover!(false) : null,
    //     cursor:
    //         widget.isTappable ? SystemMouseCursors.click : MouseCursor.defer,
    //     child: GestureDetector(
    //       onDoubleTap:
    //           widget.onDoubleTap != null
    //               ? () {
    //                 // Execute onDoubleTap callback if defined in JSON
    //                 if (widget.useJsonCallbacks &&
    //                     widget.jsonCallbacks != null &&
    //                     widget.jsonCallbacks!.containsKey('onDoubleTap')) {
    //                   _executeJsonCallback('onDoubleTap');
    //                 }

    //                 // Call standard callback
    //                 widget.onDoubleTap!();
    //               }
    //               : null,
    //       onLongPress:
    //           widget.onLongPress != null
    //               ? () {
    //                 // Execute onLongPress callback if defined in JSON
    //                 if (widget.useJsonCallbacks &&
    //                     widget.jsonCallbacks != null &&
    //                     widget.jsonCallbacks!.containsKey('onLongPress')) {
    //                   _executeJsonCallback('onLongPress');
    //                 }

    //                 // Call standard callback
    //                 widget.onLongPress!();
    //               }
    //               : null,
    //       child: textField,
    //     ),
    //   );
    // }

    return SizedBox(width: widget.inputWidth, child: textField);
  }

  // Widget _buildDecrementButton() {
  //   if (!widget.showButtons) return const SizedBox.shrink();

  //   // Create the decrement button
  //   Widget decrementButton = IconButton(
  //     icon: Icon(Icons.remove, color: widget.buttonIconColor),
  //     padding: widget.buttonPadding,
  //     constraints: const BoxConstraints(),
  //     iconSize: widget.buttonSize,
  //     splashRadius: widget.buttonSize,
  //     color: widget.buttonColor,
  //     focusColor: widget.focusColor,
  //     hoverColor: widget.hoverColor,
  //     enableFeedback: widget.enableFeedback,
  //     onPressed: _decrementValue,
  //     tooltip: 'Decrease value',
  //     style:
  //         widget.buttonShape != null
  //             ? ButtonStyle(shape: WidgetStateProperty.all(widget.buttonShape))
  //             : null,
  //   );

  //   // Apply animation if needed
  //   if (widget.hasAnimation) {
  //     decrementButton = FadeTransition(
  //       opacity: _animation,
  //       child: decrementButton,
  //     );
  //   }

  //   return decrementButton;
  // }

  // Widget _buildIncrementButton() {
  //   if (!widget.showButtons) return const SizedBox.shrink();

  //   // Create the increment button
  //   Widget incrementButton = IconButton(
  //     icon: Icon(Icons.add, color: widget.buttonIconColor),
  //     padding: widget.buttonPadding,
  //     constraints: const BoxConstraints(),
  //     iconSize: widget.buttonSize,
  //     splashRadius: widget.buttonSize,
  //     color: widget.buttonColor,
  //     focusColor: widget.focusColor,
  //     hoverColor: widget.hoverColor,
  //     enableFeedback: widget.enableFeedback,
  //     onPressed: _incrementValue,
  //     tooltip: 'Increase value',
  //     style:
  //         widget.buttonShape != null
  //             ? ButtonStyle(shape: WidgetStateProperty.all(widget.buttonShape))
  //             : null,
  //   );

  //   // Apply animation if needed
  //   if (widget.hasAnimation) {
  //     incrementButton = FadeTransition(
  //       opacity: _animation,
  //       child: incrementButton,
  //     );
  //   }

  //   return incrementButton;
  // }

  // Widget _buildMinMaxLabels() {
  //   if (!widget.showMinMaxLabels) return const SizedBox.shrink();

  //   final textStyle =
  //       widget.minMaxLabelStyle ??
  //       TextStyle(
  //         //fontSize: _getResponsiveFontSize(context),
  //         color: Colors.black,
  //       );

  //   return Row(
  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //     children: [
  //       Text(
  //         _formatValue(widget.min),
  //         style: TextStyle(fontSize: _getResponsiveInputFontSize(context)),
  //       ),
  //       Text(
  //         _formatValue(widget.max),
  //         style: TextStyle(fontSize: _getResponsiveInputFontSize(context)),
  //       ),
  //     ],
  //   );
  // }

  Widget _buildPositionedMinMaxLabels() {
    if (!widget.showMinMaxLabels) return const SizedBox.shrink();

    final textStyle =
        widget.minMaxLabelStyle ??
        TextStyle(
          fontSize: _getResponsiveInputFontSize(context),
          color: Colors.grey[600],
        );

    return Row(
      children: [
        // Min value positioned below the minus button
        if (widget.showButtons)
          SizedBox(
            width: widget.buttonSize,
            child: Text(_formatValue(widget.min), style: textStyle),
          ),
        if (widget.showButtons) SizedBox(width: widget.spacing / 2),
        // Empty space for the slider area
        Expanded(child: const SizedBox.shrink()),
        if (widget.showButtons) SizedBox(width: widget.spacing / 2),
        // Max value positioned below the plus button
        if (widget.showButtons)
          SizedBox(
            width: widget.buttonSize,
            child: Text(_formatValue(widget.max), style: textStyle),
          ),
        // Empty space to align with the text field
        SizedBox(width: widget.spacing + widget.inputWidth),
      ],
    );
  }

  Widget _buildLabel() {
    if (widget.label == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 0.0),
      child: Text(
        widget.label!,
        style:
            widget.labelTextStyle ??
            TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: _getResponsiveFontSize(context),
            ),
        textAlign: widget.labelAlignment,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Create the main content based on direction
    Widget content;
    if (widget.direction == Axis.horizontal) {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.label != null) _buildLabel(),
          Padding(
            padding: const EdgeInsets.only(top: 6),
            child: Row(
              children: [
                // Minus button on the left side of slider
                //if (widget.showButtons) _buildDecrementButton(),
                // if (widget.showButtons) SizedBox(width: widget.spacing / 2),
                // Slider in the middle
                Expanded(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: _buildSlider(),
                  ),
                ),
                if (widget.showButtons) SizedBox(width: widget.spacing / 2),
                // Plus button on the right side of slider
                //if (widget.showButtons) _buildIncrementButton(),
                SizedBox(width: widget.spacing),
                // Text input field on the far right
                _buildTextField(),
              ],
            ),
          ),
          if (widget.showMinMaxLabels)
            Padding(
              padding:  EdgeInsets.only(top: 0.0,right: 5),
              child: _buildPositionedMinMaxLabels(),
            ),
        ],
      );
    } else {
      // For vertical layout, keep buttons together with input field
      final inputWithButtons = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          //if (widget.showButtons) _buildDecrementButton(),
          _buildTextField(),
          //if (widget.showButtons) _buildIncrementButton(),
        ],
      );

      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.label != null) _buildLabel(),
          _buildSlider(),
          if (widget.showMinMaxLabels)
            Padding(
              padding: const EdgeInsets.only(bottom: 0.0),
              //child: _buildMinMaxLabels(),
            ),
          SizedBox(height: widget.spacing),
          Center(child: inputWithButtons),
        ],
      );
    }

    // Apply validation if enabled
    if (widget.useJsonValidation && !_isValid) {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          content,
          Padding(
            padding: const EdgeInsets.only(top: 4.0, left: 12.0),
            child: Text(
              'Invalid value',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 12.0,
              ),
            ),
          ),
        ],
      );
    }

    // Apply JSON styling if enabled
    if (widget.useJsonStyling && widget.jsonConfig != null) {
      // This would be implemented to apply dynamic styling from JSON
      // Not fully implemented in this example
    }

    // Apply animation to the entire widget if needed
    if (widget.hasAnimation) {
      content = AnimatedOpacity(
        opacity: 1.0,
        duration: widget.animationDuration,
        curve: widget.animationCurve,
        child: content,
      );
    }

    return content;
  }
}

/// Custom slider track shape that removes left padding
class _CustomSliderTrackShape extends SliderTrackShape
    with BaseSliderTrackShape {
  const _CustomSliderTrackShape();

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final trackHeight = sliderTheme.trackHeight!;
    final trackLeft = offset.dx; // Remove default left padding
    final trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    final trackWidth = parentBox.size.width; // Use full width
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    double additionalActiveTrackHeight = 2,
  }) {
    assert(sliderTheme.disabledActiveTrackColor != null);
    assert(sliderTheme.disabledInactiveTrackColor != null);
    assert(sliderTheme.activeTrackColor != null);
    assert(sliderTheme.inactiveTrackColor != null);
    assert(sliderTheme.thumbShape != null);

    if (sliderTheme.trackHeight == null || sliderTheme.trackHeight! <= 0) {
      return;
    }

    final ColorTween activeTrackColorTween = ColorTween(
      begin: sliderTheme.disabledActiveTrackColor,
      end: sliderTheme.activeTrackColor,
    );
    final ColorTween inactiveTrackColorTween = ColorTween(
      begin: sliderTheme.disabledInactiveTrackColor,
      end: sliderTheme.inactiveTrackColor,
    );
    final Paint activePaint =
        Paint()..color = activeTrackColorTween.evaluate(enableAnimation)!;
    final Paint inactivePaint =
        Paint()..color = inactiveTrackColorTween.evaluate(enableAnimation)!;
    final Paint leftTrackPaint;
    final Paint rightTrackPaint;
    switch (textDirection) {
      case TextDirection.ltr:
        leftTrackPaint = activePaint;
        rightTrackPaint = inactivePaint;
      case TextDirection.rtl:
        leftTrackPaint = inactivePaint;
        rightTrackPaint = activePaint;
    }

    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    final Radius trackRadius = Radius.circular(trackRect.height / 2);

    // Draw the active track (left side)
    context.canvas.drawRRect(
      RRect.fromLTRBAndCorners(
        trackRect.left,
        trackRect.top,
        thumbCenter.dx,
        trackRect.bottom,
        topLeft: trackRadius,
        bottomLeft: trackRadius,
      ),
      leftTrackPaint,
    );

    // Draw the inactive track (right side)
    context.canvas.drawRRect(
      RRect.fromLTRBAndCorners(
        thumbCenter.dx,
        trackRect.top,
        trackRect.right,
        trackRect.bottom,
        topRight: trackRadius,
        bottomRight: trackRadius,
      ),
      rightTrackPaint,
    );
  }
}

double _getResponsiveFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 16.0; // Extra Large (>1920px) - Reduced for better fit
  } else if (screenWidth >= 1440) {
    return 15.0; // Large (1440-1920px) - Reduced for better fit
  } else if (screenWidth >= 1280) {
    return 12.0; // Medium (1280-1366px) - Standard size
  } else if (screenWidth >= 768) {
    return 12.0; // Small (768-1024px) - Increased for readability
  } else {
    return 12.0; // Default for very small screens - Consistent
  }
}

double _getResponsiveInputFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 16.0; // Extra Large (>1920px) - Reduced for better fit
  } else if (screenWidth >= 1440) {
    return 15.0; // Large (1440-1920px) - Reduced for better fit
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium (1280-1366px) - Standard size
  } else if (screenWidth >= 768) {
    return 14.0; // Small (768-1024px) - Increased for readability
  } else {
    return 14.0; // Default for very small screens - Consistent
  }
}

/// Custom slider thumb shape for input slider matching basic slider design
class _CustomInputSliderThumbCircle extends SliderComponentShape {
  final double thumbRadius;
  final Color thumbColor;
  final double min;
  final double max;
  final TextStyle textStyle;

  _CustomInputSliderThumbCircle({
    required this.thumbRadius,
    required this.thumbColor,
    required this.min,
    required this.max,
    required this.textStyle,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final canvas = context.canvas;

    // Simple scaling based on activation animation
    // activationAnimation goes from 0.0 (normal) to 1.0 (pressed/dragging)
    final double scale =
        1.0 + (activationAnimation.value * 0.1); // Scale up to 1.1 when active
    final double currentRadius = thumbRadius * scale;

    // Draw white background circle
    final backgroundPaint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;
    canvas.drawCircle(center, currentRadius, backgroundPaint);

    // Draw blue border
    final borderPaint =
        Paint()
          ..color = thumbColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;
    canvas.drawCircle(center, currentRadius - 1.0, borderPaint);

    // Draw text (value inside thumb) with blue color to match border
    final textSpan = TextSpan(
      text: '${(min + (value * (max - min))).round()}',
      style: textStyle.copyWith(
        color: thumbColor,
        fontSize: textStyle.fontSize! * scale, // Scale text with thumb
      ),
    );

    final tp = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: textDirection,
    );

    tp.layout();
    tp.paint(canvas, center - Offset(tp.width / 2, tp.height / 2));
  }
}

 

  double _getResponsiveVerticalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 13.5; // Medium
    } else {
      return 13.5; // Default for very small screens
    }
  }
