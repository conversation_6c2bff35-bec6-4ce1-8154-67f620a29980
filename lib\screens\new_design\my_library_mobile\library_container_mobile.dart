import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';

import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/library_navbar_mobile.dart';
import 'package:nsl/providers/library_counts_provider.dart';
import 'package:nsl/screens/new_design/my_library_mobile/books_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/solutions_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/objects_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/agents_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';

class LibraryContainerMobile extends StatefulWidget {
  final int initialTabIndex;

  const LibraryContainerMobile({
    super.key,
    this.initialTabIndex = 0,
  });

  @override
  State<LibraryContainerMobile> createState() => _LibraryContainerMobileState();
}

class _LibraryContainerMobileState extends State<LibraryContainerMobile> {
  late int selectedTabIndex;
  final FocusNode _searchFocusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // Page titles for each tab
  final List<String> _pageTitles = [
    'library.pageTitle', // Books
    'websolution.pageTitle', // Solutions
    'webobject.pageTitle', // Objects
    'webagent.pageTitle', // Agents
  ];

  // Search hints for each tab
  final List<String> _searchHints = [
    'Search books...',
    'Search solutions...',
    'Search objects...',
    'Search agents...',
  ];

  @override
  void initState() {
    super.initState();
    selectedTabIndex = widget.initialTabIndex;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase().trim();
    });
  }

  void _onTabChanged(int index) {
    setState(() {
      selectedTabIndex = index;
      // Clear search when switching tabs
      _searchController.clear();
      _searchQuery = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfff6f6f6),
      drawer: const CustomDrawer(),
      appBar: _buildAppBar(),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTopNavigation(),
          _buildSearchAndCreateSection(),
          Expanded(
            child: _buildCurrentTabContent(),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// Builds app bar with hamburger menu and page title
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          Expanded(
            child: Text(
              AppLocalizations.of(context)
                  .translate(_pageTitles[selectedTabIndex]),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 56),
        ],
      ),
    );
  }

  /// Builds top navigation tabs
  Widget _buildTopNavigation() {
    return Consumer<LibraryCountsProvider>(
      builder: (context, countsProvider, child) {
        return LibraryNavbarMobile(
          selectedTabIndex: selectedTabIndex,
          onTabChanged: _onTabChanged,
          booksCount: countsProvider.booksCount,
          solutionsCount: countsProvider.solutionsCount,
          objectsCount: countsProvider.objectsCount,
          agentsCount: countsProvider.agentsCount,
        );
      },
    );
  }

  /// Builds search and create section
  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  decoration: InputDecoration(
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    hintText: _searchHints[selectedTabIndex],
                    border: InputBorder.none,
                    hintStyle: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),
            ),
            _LibrarySvgButton(
              iconPath: 'assets/images/search.svg',
              onPressed: () {},
              size: 20,
            ),
            Container(
              height: 24,
              width: 1,
              color: Colors.grey.shade200,
              margin: const EdgeInsets.symmetric(horizontal: 4),
            ),
            _LibrarySvgButton(
              iconPath: 'assets/images/filter-icon.svg',
              onPressed: () {},
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the content for the currently selected tab
  Widget _buildCurrentTabContent() {
    switch (selectedTabIndex) {
      case 0:
        return BooksLibraryMobile(
          showNavigationBar: false,
          searchQuery: _searchQuery,
        );
      case 1:
        return SolutionsLibraryMobile(
          showNavigationBar: false,
          searchQuery: _searchQuery,
        );
      case 2:
        return ObjectsLibraryMobile(
          showNavigationBar: false,
          searchQuery: _searchQuery,
        );
      case 3:
        return AgentsLibraryMobile(
          showNavigationBar: false,
          searchQuery: _searchQuery,
        );
      default:
        return BooksLibraryMobile(
          showNavigationBar: false,
          searchQuery: _searchQuery,
        );
    }
  }

  /// Builds floating action button
  Widget _buildFloatingActionButton() {
    return SizedBox(
      width: 46,
      height: 46,
      child: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateBookMobile(),
            ),
          );
        },
        backgroundColor: const Color(0xff0058FF),
        foregroundColor: Colors.white,
        shape: const CircleBorder(),
        child: const Icon(Icons.add),
      ),
    );
  }
}

/// Custom SVG button widget for search and filter actions
class _LibrarySvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _LibrarySvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 24,
  });

  @override
  State<_LibrarySvgButton> createState() => _LibrarySvgButtonState();
}

class _LibrarySvgButtonState extends State<_LibrarySvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isPressed ? Colors.grey.shade100 : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
            colorFilter: ColorFilter.mode(
              isPressed ? Colors.grey.shade600 : Colors.grey.shade700,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }
}
