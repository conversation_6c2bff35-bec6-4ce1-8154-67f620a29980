import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import '../../../../models/nsl_hierarchy_model.dart';

class SimplifiedNSLCard extends StatelessWidget {
  final NSLNode node;
  final bool isSelected;
  final bool isHighlightedInPath;
  final VoidCallback onTitleTap; // For title container and green dot - opens side panel
  final VoidCallback onInfoTap;  // For info container - changes border and expands children
  final bool hasInfoTapped; // To track if info container was tapped for blue border
  
  const SimplifiedNSLCard({
    super.key,
    required this.node,
    required this.isSelected,
    required this.isHighlightedInPath,
    required this.onTitleTap,
    required this.onInfoTap,
    this.hasInfoTapped = false,
  });

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center, // Center align all children
        children: [
          // Green dot indicator positioned above the title container - tappable for side panel
          InkWell(
            onTap: onTitleTap,
            child: Center(
              child: Container(
                width: 10,
                height: 10,
                decoration: const BoxDecoration(
                  color: Color(0xFF4CAF50), // Green dot
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        
          
          // Title Container - tappable for side panel
          InkWell(
            onTap: onTitleTap,
            child: _buildTitleContainer(),
          ),
          
          
          
          // Info Container - tappable for expansion
          InkWell(
            onTap: onInfoTap,
            child: _buildInfoContainer(),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleContainer() {
    return IntrinsicWidth(
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ?  Color(0xFF0058FF) : node.levelColor, // Blue background when selected
          borderRadius: BorderRadius.circular(2),
          border: Border.all(
            color:   hasInfoTapped
                ? Color(0xFF0058FF):// Blue border when selected
                 Color(0xFFD1D1D1),    // Default gray border
            width: isSelected ? 2 : 1,  // Thicker border when selected
          ),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.size10, vertical:AppSpacing.size6),
          child: Text(
            
            node.title.length > 20 ? '${node.title.substring(0, 12)}...' : node.title,
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontWeight: FontManager.regular, // Bold when selected
              fontFamily: FontManager.fontFamilyTiemposText,
              color:  isSelected ?  Colors.white: Colors.black, // Blue text when selected
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
    );
  }

  Widget _buildInfoContainer() {
    return IntrinsicWidth(
      child: Container(
        constraints: const BoxConstraints(
          minWidth: 123,
        //  maxWidth: 250,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(2),
          border: Border.all(
            color: 
          
                // : isHighlightedInPath
                //     ? Colors.orange.shade300
                    Color(0xFFD1D1D1),),
          
        ),
        child: Container(
           padding: const EdgeInsets.symmetric(horizontal: AppSpacing.size10, vertical:AppSpacing.size6),
      
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //  mainAxisSize: MainAxisSize.min,
            children: [
              // ID
              Text(
                'ID: ${node.id}',
               style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),),
                      
            const SizedBox(width: AppSpacing.size20),
              // NP (total_bets)
              Text(
                'NP: ${node.totalBets}',
               style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontManager.regular,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
