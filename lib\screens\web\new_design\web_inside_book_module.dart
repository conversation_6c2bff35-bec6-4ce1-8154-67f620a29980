import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/hover_nav_item.dart';
import 'package:nsl/screens/web/new_design/web_add_modules_page.dart';
import 'package:nsl/screens/web/new_design/solution_modules_page.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class WebInsideBookModule extends StatefulWidget {
  const WebInsideBookModule({super.key});

  @override
  State<WebInsideBookModule> createState() => _WebInsideBookModuleState();
}

class _WebInsideBookModuleState extends State<WebInsideBookModule> {
  // Search state
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // Module data - moved to parent widget
  final List<Map<String, dynamic>> _allModuleData = [
    {
      'title': 'Leave management',
      'svgIcon': 'assets/images/book_nsl.svg',
      'totalSolutions': 2,
      'createdOn': '09/05/2025',
      'isExpandable': true,
      'subItems': [
        {'title': 'Apply leave', 'createdOn': '09/05/2025'},
        {'title': 'Approve/Reject leave', 'createdOn': '09/05/2025'},
      ],
    },
    {
      'title': 'Create PO',
      'svgIcon': 'assets/images/square-box-uncheck.svg',
      'totalSolutions': 1,
      'createdOn': '09/05/2025',
      'isExpandable': false,
      'subItems': [],
    },
    {
      'title': 'Inventory Management',
      'svgIcon': 'assets/images/book_nsl.svg',
      'totalSolutions': 3,
      'createdOn': '08/05/2025',
      'isExpandable': true,
      'subItems': [
        {'title': 'Add inventory item', 'createdOn': '08/05/2025'},
        {'title': 'Update stock levels', 'createdOn': '08/05/2025'},
        {'title': 'Generate inventory report', 'createdOn': '08/05/2025'},
      ],
    },
    {
      'title': 'User Authentication',
      'svgIcon': 'assets/images/book_nsl.svg',
      'totalSolutions': 2,
      'createdOn': '07/05/2025',
      'isExpandable': true,
      'subItems': [
        {'title': 'Login user', 'createdOn': '07/05/2025'},
        {'title': 'Reset password', 'createdOn': '07/05/2025'},
      ],
    },
  ];
  // Filtered data based on search
  List<Map<String, dynamic>> _filteredModuleData = [];
  String _searchText = '';

  @override
  void initState() {
    super.initState();
    _filteredModuleData = List.from(_allModuleData);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  // Handle search text changes with filtering logic
  void _onSearchChanged() {
    final searchText = _searchController.text.toLowerCase().trim();
    setState(() {
      _searchText = searchText;

      if (searchText.isEmpty) {
        // Show all modules when search is empty
        _filteredModuleData = List.from(_allModuleData);
      } else {
        // Filter modules based on search text
        _filteredModuleData = _allModuleData.where((module) {
          // Check if module title matches
          bool titleMatches = module['title'].toString().toLowerCase().contains(searchText);

          // Check if any sub-item matches
          bool subItemMatches = false;
          if (module['subItems'] != null && module['subItems'].isNotEmpty) {
            subItemMatches = module['subItems'].any((subItem) =>
              subItem['title'].toString().toLowerCase().contains(searchText)
            );
          }

          return titleMatches || subItemMatches;
        }).toList();

        // If a module is included because of sub-item match,
        // filter sub-items to show only matching ones
        _filteredModuleData = _filteredModuleData.map((module) {
          if (module['subItems'] != null && module['subItems'].isNotEmpty) {
            // Check if module title matches
            bool titleMatches = module['title'].toString().toLowerCase().contains(searchText);

            if (!titleMatches) {
              // If module title doesn't match, filter sub-items
              var filteredSubItems = module['subItems'].where((subItem) =>
                subItem['title'].toString().toLowerCase().contains(searchText)
              ).toList();

              // Create a copy of the module with filtered sub-items
              var moduleClone = Map<String, dynamic>.from(module);
              moduleClone['subItems'] = filteredSubItems;
              moduleClone['totalSolutions'] = filteredSubItems.length;
              return moduleClone;
            }
          }
          return module;
        }).toList();
      }
    });
    
    debugPrint('Search text: $searchText, Found ${_filteredModuleData.length} modules');
  }

  // Focus search input when search icon is clicked
  void _focusSearchInput() {
    _searchFocusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xffF7F9FB),
      child: Padding(
        padding:
            const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0),
        child: Column(
          children: [
            const HoverNavItems(),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Book Name text
                Expanded(
                  child: Text(
                    'Book Name',
                    style: FontManager.getCustomStyle(
                      fontSize: FontManager.s14,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ),

                // Search bar with filter
                Expanded(
                  child: Container(
                    width: MediaQuery.of(context).size.width / 3.722,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Row(
                      children: [
                        // Search text field
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 16.0),
                            child: TextField(
                              controller: _searchController,
                              focusNode: _searchFocusNode,
                              cursorHeight: 16,
                              decoration: InputDecoration(
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                hintText: 'Search modules and solutions...',
                                border: InputBorder.none,
                                hintStyle: TextStyle(
                                    fontSize: 14, color: Color(0xff939393)),
                                isDense: true,
                                contentPadding: EdgeInsets.zero,
                                filled: false,
                                fillColor: Colors.transparent,
                              ),
                            ),
                          ),
                        ),
                        // Search icon
                        _HoverSvgButton(
                          normalIconPath: 'assets/images/search.svg',
                          hoverIconPath: 'assets/images/search.svg',
                          onPressed: () {
                            _focusSearchInput();
                          },
                          imageWidth: 20,
                          imageHeight: 20,
                          showBorderOnHover: false,
                        ),
                        // Divider between search and filter
                        Container(
                          height: 23,
                          width: 1,
                          color: Colors.grey.shade200,
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                        ),
                        // Filter icon
                        _HoverSvgButton(
                          normalIconPath: 'assets/images/filter-icon.svg',
                          hoverIconPath: 'assets/images/filter-hover.svg',
                          onPressed: () {
                            // Add filter logic here
                          },
                          imageWidth: 32,
                          imageHeight: 32,
                          showBorderOnHover: false,
                        ),
                        const SizedBox(width: 8),
                      ],
                    ),
                  ),
                ),

                // Create buttons
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      _SvgTextButton(
                        text: 'Add Solutions',
                        svgPath: 'assets/images/square-box-uncheck.svg',
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SolutionModulesPage(),
                            ),
                          );
                        },
                      ),
                      SizedBox(width: 12),
                      _SvgTextButton(
                        text: 'Create Modules',
                        svgPath: 'assets/images/book_nsl.svg',
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => WebAddModulesPage(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),

            // Search results info
            if (_searchText.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                child: Text(
                  _filteredModuleData.isEmpty
                    ? 'No modules or solutions found for "$_searchText"'
                    : 'Found ${_filteredModuleData.length} module(s) matching "$_searchText"',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: _filteredModuleData.isEmpty ? Colors.red : Colors.blue,
                  ),
                ),
              ),

            // Content table section
            Container(
              width: double.infinity,
              child: Column(
                children: [
                  // Table header
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 10),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 4,
                          child: Text(
                            'Modules / Solutions',
                            style: FontManager.getCustomStyle(
                              fontSize: FontManager.s12,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'Total Solutions',
                            style: FontManager.getCustomStyle(
                              fontSize: FontManager.s12,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            'Created on',
                            style: FontManager.getCustomStyle(
                              fontSize: FontManager.s12,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Table content with filtered data
                  _ModulesAccordionList(
                    moduleData: _filteredModuleData,
                    searchText: _searchText,
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

class _ModulesAccordionList extends StatefulWidget {
  final List<Map<String, dynamic>> moduleData;
  final String searchText;

  const _ModulesAccordionList({
    required this.moduleData,
    required this.searchText,
  });

  @override
  State<_ModulesAccordionList> createState() => _ModulesAccordionListState();
}

class _ModulesAccordionListState extends State<_ModulesAccordionList> {
  Set<int> expandedItems = {};

  @override
  void didUpdateWidget(_ModulesAccordionList oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Auto-expand modules when searching and they have matching sub-items
    if (widget.searchText.isNotEmpty && widget.searchText != oldWidget.searchText) {
      Set<int> newExpandedItems = {};
      for (int i = 0; i < widget.moduleData.length; i++) {
        final module = widget.moduleData[i];
        if (module['subItems'] != null && module['subItems'].isNotEmpty) {
          // Check if module title doesn't match but sub-items do
          bool titleMatches = module['title'].toString().toLowerCase().contains(widget.searchText.toLowerCase());
          if (!titleMatches) {
            bool hasMatchingSubItems = module['subItems'].any((subItem) =>
              subItem['title'].toString().toLowerCase().contains(widget.searchText.toLowerCase())
            );
            if (hasMatchingSubItems) {
              newExpandedItems.add(i);
            }
          }
        }
      }
      setState(() {
        expandedItems.addAll(newExpandedItems);
      });
    }
  }

  // Highlight matching text
  Widget _buildHighlightedText(String text, String searchText, TextStyle style) {
    if (searchText.isEmpty) {
      return Text(text, style: style);
    }

    final lowerText = text.toLowerCase();
    final lowerSearch = searchText.toLowerCase();

    if (!lowerText.contains(lowerSearch)) {
      return Text(text, style: style);
    }

    final startIndex = lowerText.indexOf(lowerSearch);
    final endIndex = startIndex + searchText.length;

    return RichText(
      text: TextSpan(
        children: [
          if (startIndex > 0)
            TextSpan(
              text: text.substring(0, startIndex),
              style: style,
            ),
          TextSpan(
            text: text.substring(startIndex, endIndex),
            style: style.copyWith(
              backgroundColor: Colors.yellow.withOpacity(0.3),
              fontWeight: FontWeight.w600,
            ),
          ),
          if (endIndex < text.length)
            TextSpan(
              text: text.substring(endIndex),
              style: style,
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.moduleData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No modules found',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Try adjusting your search terms',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s12,
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        for (int index = 0; index < widget.moduleData.length; index++) ...[
          _buildModuleRow(widget.moduleData[index], index),
          if (expandedItems.contains(index) &&
              widget.moduleData[index]['subItems'].isNotEmpty)
            ...widget.moduleData[index]['subItems']
                .map<Widget>((subItem) => _buildSubItemRow(subItem))
                .toList(),
          const SizedBox(height: 2),
        ],
      ],
    );
  }

  Widget _buildModuleRow(Map<String, dynamic> module, int index) {
    bool isExpanded = expandedItems.contains(index);
    bool hasSubItems = module['subItems'].isNotEmpty;

    return Container(
      decoration: BoxDecoration(
        color: isExpanded ? Color(0xffDBE9F2) : Colors.white,
      ),
      child: InkWell(
        onTap: hasSubItems
            ? () {
                setState(() {
                  if (isExpanded) {
                    expandedItems.remove(index);
                  } else {
                    expandedItems.add(index);
                  }
                });
              }
            : null,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          child: Row(
            children: [
              Expanded(
                flex: 4,
                child: Row(
                  children: [
                    if (hasSubItems) ...[
                      Icon(
                        isExpanded
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        size: 18,
                        color: Colors.black,
                      ),
                      SizedBox(width: 8),
                    ] else ...[
                      SizedBox(width: 26),
                    ],
                    SvgPicture.asset(
                      module['svgIcon'],
                      width: 12,
                      height: 12,
                      colorFilter: ColorFilter.mode(
                        Colors.black54,
                        BlendMode.srcIn,
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: _buildHighlightedText(
                        module['title'],
                        widget.searchText,
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: isExpanded ? FontWeight.w600 : FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  module['totalSolutions'].toString(),
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s12,
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      module['createdOn'],
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    if (!isExpanded) ...[
                      Image.asset(
                        'assets/images/edit-square.png',
                        color: Colors.black,
                      ),
                      SizedBox(width: 10),
                    ],
                    SizedBox(width: 10),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubItemRow(Map<String, dynamic> subItem) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xffDBE9F2),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        child: Row(
          children: [
            Expanded(
              flex: 4,
              child: Padding(
                padding: const EdgeInsets.only(left: 48),
                child: Row(
                  children: [
                    SvgPicture.asset(
                      'assets/images/square-box-uncheck.svg',
                      width: 12,
                      height: 12,
                      colorFilter: ColorFilter.mode(
                        Colors.black54,
                        BlendMode.srcIn,
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: _buildHighlightedText(
                        subItem['title'],
                        widget.searchText,
                        FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: SizedBox(),
            ),
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 6),
                    child: Text(
                      subItem['createdOn'],
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s12,
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _HoverSvgButton extends StatefulWidget {
  final String normalIconPath;
  final String hoverIconPath;
  final VoidCallback onPressed;
  final double imageWidth;
  final double imageHeight;
  final bool showBorderOnHover;

  const _HoverSvgButton({
    required this.normalIconPath,
    required this.hoverIconPath,
    required this.onPressed,
    this.imageWidth = 18,
    this.imageHeight = 18,
    this.showBorderOnHover = true,
  });

  @override
  State<_HoverSvgButton> createState() => _HoverSvgButtonState();
}

class _HoverSvgButtonState extends State<_HoverSvgButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: widget.showBorderOnHover
              ? Border.all(
                  color: isHovered ? Color(0xff0058FF) : Colors.transparent,
                  width: 1.0,
                )
              : null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: IconButton(
          icon: SvgPicture.asset(
            isHovered ? widget.hoverIconPath : widget.normalIconPath,
            width: widget.imageWidth,
            height: widget.imageHeight,
          ),
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _SvgTextButton extends StatelessWidget {
  final String text;
  final String svgPath;
  final VoidCallback onPressed;

  const _SvgTextButton({
    Key? key,
    required this.text,
    required this.svgPath,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      focusColor: Colors.transparent,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              svgPath,
              width: 12,
              height: 12,
              colorFilter: ColorFilter.mode(
                Colors.black54,
                BlendMode.srcIn,
              ),
            ),
            SizedBox(width: 4),
            Text(
              text,
              style: TextStyle(
                fontSize: FontManager.s12,
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
