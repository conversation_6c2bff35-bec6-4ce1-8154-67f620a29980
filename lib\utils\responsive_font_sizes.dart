// responsive_font_sizes.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:math' as math;

/// A comprehensive class for handling responsive font sizes across different devices
/// Supports multiple responsive strategies and follows Material Design typography scale
class ResponsiveFontSizes {
  // Base breakpoints for responsive design
  static const double _mobileBreakpoint = 600;
  static const double _tabletBreakpoint = 1024;
  static const double _desktopBreakpoint = 1440;

  // Base font scale factors for different devices
  static const double _mobileScale = 0.85;
  static const double _tabletScale = 1.0;
  static const double _desktopScale = 1.15;
  static const double _webScale = 1.25;

  /// Get device type based on screen width and platform
  static DeviceType _getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < _mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < _tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      // For screens >= 1024px, differentiate based on platform
      if (kIsWeb) {
        return DeviceType.desktop;
      } else {
        return DeviceType.tablet;
      }
    }
  }

  /// Get responsive scale factor based on device type
  static double _getScaleFactor(BuildContext context) {
    final deviceType = _getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return _mobileScale;
      case DeviceType.tablet:
        return _tabletScale;
      case DeviceType.desktop:
        return _desktopScale;
      case DeviceType.web:
        return _webScale;
    }
  }

  /// Get exact font size based on device type (no interpolation)
  static double _getExactSize(BuildContext context, {
    required double mobile,
    required double tablet,
    required double desktop,
    required double web,
  }) {
    final deviceType = _getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet;
      case DeviceType.desktop:
        return desktop;
      case DeviceType.web:
        return web;
    }
  }

  /// Apply text scale factor from accessibility settings
  static double _applyTextScaleFactor(BuildContext context, double fontSize) {
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    // Clamp text scale factor to prevent extreme sizes
    final clampedScaleFactor = textScaleFactor.clamp(0.8, 2.0);
    return fontSize * clampedScaleFactor;
  }

  // ===============================
  // MATERIAL DESIGN DISPLAY STYLES
  // ===============================

  /// Display Large - Largest text on screen, short and important text
  /// Usage: Hero headlines, large numbers, one-off large text
  static double displayLarge(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 40,
      tablet: 48,
      desktop: 57,
      web: 64,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Display Medium - High-emphasis text that's shorter than headlines
  /// Usage: Large headings, featured content titles
  static double displayMedium(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 32,
      tablet: 40,
      desktop: 45,
      web: 52,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Display Small - Medium-emphasis text that's shorter than headlines
  /// Usage: Section headers, card titles
  static double displaySmall(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 28,
      tablet: 32,
      desktop: 36,
      web: 40,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  // ===============================
  // MATERIAL DESIGN HEADLINE STYLES
  // ===============================

  /// Headline Large - High-emphasis text for main headings
  /// Usage: Page titles, article headlines
  static double headlineLarge(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 24,
      tablet: 28,
      desktop: 32,
      web: 36,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Headline Medium - High-emphasis text for section headings
  /// Usage: Section titles, card headers
  static double headlineMedium(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 20,
      tablet: 24,
      desktop: 28,
      web: 32,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Headline Small - High-emphasis text for subsection headings
  /// Usage: Subsection titles, form section headers
  static double headlineSmall(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 18,
      tablet: 22,
      desktop: 24,
      web: 28,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  // ===============================
  // MATERIAL DESIGN TITLE STYLES
  // ===============================

  /// Title Large - Medium-emphasis text for important content
  /// Usage: Prominent buttons, important links, card titles
  static double titleLarge(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 16,
      tablet: 20,
      desktop: 22,
      web: 24,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Title Medium - Medium-emphasis text for content hierarchy
  /// Usage: List item titles, form labels, tab labels
  static double titleMedium(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 14,
      tablet: 16,
      desktop: 18,
      web: 20,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Title Small - Medium-emphasis text for smaller content
  /// Usage: Overline text, small headings, dense content
  static double titleSmall(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 12,
      tablet: 14,
      desktop: 16,
      web: 18,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  // ===============================
  // MATERIAL DESIGN BODY STYLES
  // ===============================

  /// Body Large - Emphasized body text for main content
  /// Usage: Article text, main content, emphasis text
  static double bodyLarge(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 14,
      tablet: 16,
      desktop: 16,
      web: 18,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Body Medium - Regular body text for main content
  /// Usage: Default text, paragraphs, list items
  static double bodyMedium(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 12,
      tablet: 14,
      desktop: 14,
      web: 16,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Body Small - Lower-emphasis body text
  /// Usage: Supporting text, descriptions, secondary information
  static double bodySmall(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 11,
      tablet: 12,
      desktop: 12,
      web: 14,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  // ===============================
  // MATERIAL DESIGN LABEL STYLES
  // ===============================

  /// Label Large - Call-to-action text on buttons
  /// Usage: Button text, emphasized links, prominent actions
  static double labelLarge(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 12,
      tablet: 14,
      desktop: 14,
      web: 16,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Label Medium - Text on smaller buttons and links
  /// Usage: Small button text, navigation links, tab text
  static double labelMedium(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 11,
      tablet: 12,
      desktop: 12,
      web: 14,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Label Small - Text for small elements
  /// Usage: Chips, badges, timestamps, tiny labels
  static double labelSmall(BuildContext context, {bool applyTextScale = true}) {
    final fontSize = _getExactSize(
      context,
      mobile: 9,
      tablet: 10,
      desktop: 11,
      web: 12,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  // ===============================
  // UTILITY METHODS
  // ===============================

  /// Custom responsive font size with manual breakpoint values
  static double custom(
    BuildContext context, {
    required double mobile,
    required double tablet,
    required double desktop,
    double? web,
    bool applyTextScale = false,
  }) {
    final fontSize = _getExactSize(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      web: web ?? desktop * 1.2,
    );
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Scale any font size based on current device
  static double scale(BuildContext context, double baseSize, {bool applyTextScale = true}) {
    final scaledSize = baseSize * _getScaleFactor(context);
    return applyTextScale ? _applyTextScaleFactor(context, scaledSize) : scaledSize;
  }

  /// Get font size based on viewport width percentage
  static double viewport(BuildContext context, double percentage, {
    double? minSize,
    double? maxSize,
    bool applyTextScale = true,
  }) {
    final width = MediaQuery.of(context).size.width;
    double fontSize = width * (percentage / 100);
    
    if (minSize != null) fontSize = math.max(fontSize, minSize);
    if (maxSize != null) fontSize = math.min(fontSize, maxSize);
    
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  /// Get font size for specific device type
  static double forDevice(
    BuildContext context,
    DeviceType deviceType,
    double baseSize, {
    bool applyTextScale = true,
  }) {
    double scaleFactor;
    switch (deviceType) {
      case DeviceType.mobile:
        scaleFactor = _mobileScale;
        break;
      case DeviceType.tablet:
        scaleFactor = _tabletScale;
        break;
      case DeviceType.desktop:
        scaleFactor = _desktopScale;
        break;
      case DeviceType.web:
        scaleFactor = _webScale;
        break;
    }
    
    final fontSize = baseSize * scaleFactor;
    return applyTextScale ? _applyTextScaleFactor(context, fontSize) : fontSize;
  }

  // ===============================
  // THEME INTEGRATION
  // ===============================

  /// Generate a complete responsive TextTheme
  static TextTheme getResponsiveTextTheme(BuildContext context, {
    String? fontFamily,
    Color? color,
  }) {
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: displayLarge(context),
        fontWeight: FontWeight.w300,
        letterSpacing: -1.5,
        fontFamily: fontFamily,
        color: color,
      ),
      displayMedium: TextStyle(
        fontSize: displayMedium(context),
        fontWeight: FontWeight.w300,
        letterSpacing: -0.5,
        fontFamily: fontFamily,
        color: color,
      ),
      displaySmall: TextStyle(
        fontSize: displaySmall(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.0,
        fontFamily: fontFamily,
        color: color,
      ),
      headlineLarge: TextStyle(
        fontSize: headlineLarge(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        fontFamily: fontFamily,
        color: color,
      ),
      headlineMedium: TextStyle(
        fontSize: headlineMedium(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.0,
        fontFamily: fontFamily,
        color: color,
      ),
      headlineSmall: TextStyle(
        fontSize: headlineSmall(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.0,
        fontFamily: fontFamily,
        color: color,
      ),
      titleLarge: TextStyle(
        fontSize: titleLarge(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
        fontFamily: fontFamily,
        color: color,
      ),
      titleMedium: TextStyle(
        fontSize: titleMedium(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
        fontFamily: fontFamily,
        color: color,
      ),
      titleSmall: TextStyle(
        fontSize: titleSmall(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        fontFamily: fontFamily,
        color: color,
      ),
      bodyLarge: TextStyle(
        fontSize: bodyLarge(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        fontFamily: fontFamily,
        color: color,
      ),
      bodyMedium: TextStyle(
        fontSize: bodyMedium(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        fontFamily: fontFamily,
        color: color,
      ),
      bodySmall: TextStyle(
        fontSize: bodySmall(context),
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        fontFamily: fontFamily,
        color: color,
      ),
      labelLarge: TextStyle(
        fontSize: labelLarge(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 1.25,
        fontFamily: fontFamily,
        color: color,
      ),
      labelMedium: TextStyle(
        fontSize: labelMedium(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 1.25,
        fontFamily: fontFamily,
        color: color,
      ),
      labelSmall: TextStyle(
        fontSize: labelSmall(context),
        fontWeight: FontWeight.w500,
        letterSpacing: 1.5,
        fontFamily: fontFamily,
        color: color,
      ),
    );
  }

  // ===============================
  // HELPER METHODS
  // ===============================

  /// Get appropriate line height for given font size
  static double getLineHeight(double fontSize) {
    if (fontSize >= 32) return 1.2;
    if (fontSize >= 24) return 1.3;
    if (fontSize >= 16) return 1.4;
    if (fontSize >= 14) return 1.5;
    return 1.6;
  }

  /// Get appropriate letter spacing for given font size
  static double getLetterSpacing(double fontSize) {
    if (fontSize >= 32) return -0.5;
    if (fontSize >= 24) return 0.0;
    if (fontSize >= 16) return 0.15;
    if (fontSize >= 14) return 0.25;
    return 0.4;
  }

  /// Get appropriate font weight for emphasis level
  static FontWeight getFontWeight(EmphasisLevel emphasis) {
    switch (emphasis) {
      case EmphasisLevel.high:
        return FontWeight.w700;
      case EmphasisLevel.medium:
        return FontWeight.w500;
      case EmphasisLevel.low:
        return FontWeight.w400;
      case EmphasisLevel.disabled:
        return FontWeight.w300;
    }
  }

  /// Get current device type
  static DeviceType getDeviceType(BuildContext context) {
    return _getDeviceType(context);
  }

  /// Check if current device is mobile
  static bool isMobile(BuildContext context) {
    return _getDeviceType(context) == DeviceType.mobile;
  }

  /// Check if current device is tablet
  static bool isTablet(BuildContext context) {
    return _getDeviceType(context) == DeviceType.tablet;
  }

  /// Check if current device is desktop
  static bool isDesktop(BuildContext context) {
    return _getDeviceType(context) == DeviceType.desktop;
  }

  /// Check if current device is web
  static bool isWeb(BuildContext context) {
    return _getDeviceType(context) == DeviceType.web;
  }

  /// Check if running on web platform
  static bool get isWebPlatform => kIsWeb;
}

// ===============================
// ENUMS AND SUPPORTING CLASSES
// ===============================

enum DeviceType { mobile, tablet, desktop, web }

enum EmphasisLevel { high, medium, low, disabled }

/// Extension methods for DeviceType enum
extension DeviceTypeExtension on DeviceType {
  String get name {
    switch (this) {
      case DeviceType.mobile:
        return 'Mobile';
      case DeviceType.tablet:
        return 'Tablet';
      case DeviceType.desktop:
        return 'Desktop';
      case DeviceType.web:
        return 'Web';
    }
  }

  bool get isMobile => this == DeviceType.mobile;
  bool get isTablet => this == DeviceType.tablet;
  bool get isDesktop => this == DeviceType.desktop;
  bool get isWeb => this == DeviceType.web;
}

/// Responsive Text Style Builder
class ResponsiveTextStyle {
  final BuildContext context;

  ResponsiveTextStyle._(this.context);

  factory ResponsiveTextStyle.of(BuildContext context) {
    return ResponsiveTextStyle._(context);
  }

  TextStyle get displayLarge => TextStyle(
    fontSize: ResponsiveFontSizes.displayLarge(context),
    fontWeight: FontWeight.w300,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.displayLarge(context, applyTextScale: false)),
  );

  TextStyle get displayMedium => TextStyle(
    fontSize: ResponsiveFontSizes.displayMedium(context),
    fontWeight: FontWeight.w300,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.displayMedium(context, applyTextScale: false)),
  );

  TextStyle get displaySmall => TextStyle(
    fontSize: ResponsiveFontSizes.displaySmall(context),
    fontWeight: FontWeight.w400,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.displaySmall(context, applyTextScale: false)),
  );

  TextStyle get headlineLarge => TextStyle(
    fontSize: ResponsiveFontSizes.headlineLarge(context),
    fontWeight: FontWeight.w400,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.headlineLarge(context, applyTextScale: false)),
  );

  TextStyle get headlineMedium => TextStyle(
    fontSize: ResponsiveFontSizes.headlineMedium(context),
    fontWeight: FontWeight.w400,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.headlineMedium(context, applyTextScale: false)),
  );

  TextStyle get headlineSmall => TextStyle(
    fontSize: ResponsiveFontSizes.headlineSmall(context),
    fontWeight: FontWeight.w400,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.headlineSmall(context, applyTextScale: false)),
  );

  TextStyle get titleLarge => TextStyle(
    fontSize: ResponsiveFontSizes.titleLarge(context),
    fontWeight: FontWeight.w500,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.titleLarge(context, applyTextScale: false)),
  );

  TextStyle get titleMedium => TextStyle(
    fontSize: ResponsiveFontSizes.titleMedium(context),
    fontWeight: FontWeight.w500,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.titleMedium(context, applyTextScale: false)),
  );

  TextStyle get titleSmall => TextStyle(
    fontSize: ResponsiveFontSizes.titleSmall(context),
    fontWeight: FontWeight.w500,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.titleSmall(context, applyTextScale: false)),
  );

  TextStyle get bodyLarge => TextStyle(
    fontSize: ResponsiveFontSizes.bodyLarge(context),
    fontWeight: FontWeight.w400,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.bodyLarge(context, applyTextScale: false)),
  );

  TextStyle get bodyMedium => TextStyle(
    fontSize: ResponsiveFontSizes.bodyMedium(context),
    fontWeight: FontWeight.w400,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.bodyMedium(context, applyTextScale: false)),
  );

  TextStyle get bodySmall => TextStyle(
    fontSize: ResponsiveFontSizes.bodySmall(context),
    fontWeight: FontWeight.w400,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.bodySmall(context, applyTextScale: false)),
  );

  TextStyle get labelLarge => TextStyle(
    fontSize: ResponsiveFontSizes.labelLarge(context),
    fontWeight: FontWeight.w500,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.labelLarge(context, applyTextScale: false)),
  );

  TextStyle get labelMedium => TextStyle(
    fontSize: ResponsiveFontSizes.labelMedium(context),
    fontWeight: FontWeight.w500,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.labelMedium(context, applyTextScale: false)),
  );

  TextStyle get labelSmall => TextStyle(
    fontSize: ResponsiveFontSizes.labelSmall(context),
    fontWeight: FontWeight.w500,
    height: ResponsiveFontSizes.getLineHeight(ResponsiveFontSizes.labelSmall(context, applyTextScale: false)),
  );

  /// Custom style with responsive sizing
  TextStyle custom({
    required double mobile,
    required double tablet,
    required double desktop,
    double? web,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
  }) {
    final fontSize = ResponsiveFontSizes.custom(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      web: web,
    );

    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight ?? FontWeight.w400,
      color: color,
      letterSpacing: letterSpacing ?? ResponsiveFontSizes.getLetterSpacing(fontSize),
      height: height ?? ResponsiveFontSizes.getLineHeight(fontSize),
    );
  }
}