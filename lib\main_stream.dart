// main.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// services/api_service.dart
import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
// providers/chat_provider.dart

// screens/chat_screen.dart

// widgets/message_widget.dart
import 'package:flutter/services.dart';
// widgets/input_widget.dart

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ChatProvider()),
      ],
      child: MaterialApp(
        title: 'Claude Streaming Chat',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
          fontFamily: 'Inter',
        ),
        home: ChatScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

// models/message.dart
enum MessageType { user, assistant, system, error }

enum MessageStatus { sending, streaming, completed, failed }

class Message {
  final String id;
  final String content;
  final MessageType type;
  final DateTime timestamp;
  final MessageStatus status;

  Message({
    required this.id,
    required this.content,
    required this.type,
    required this.timestamp,
    this.status = MessageStatus.completed,
  });

  Message copyWith({
    String? id,
    String? content,
    MessageType? type,
    DateTime? timestamp,
    MessageStatus? status,
  }) {
    return Message(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
    );
  }
}

class ApiService {
  static const String baseUrl = 'http://10.26.1.52:8445';
  late final Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: Duration(seconds: 30),
      receiveTimeout: Duration(seconds: 30),
      headers: {
        'Accept': 'application/octet-stream',
        'Content-Type': 'application/json',
      },
    ));

    // Add interceptors for logging (optional)
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: false, // Don't log streaming response body
      logPrint: (obj) => print(obj),
    ));
  }

  Stream<String> sendStreamingMessage(String message) async* {
    try {
      final response = await _dio.post<ResponseBody>(
        '/chat',
        data: {'message': message},
        options: Options(
          responseType: ResponseType.stream,
          headers: {
            'Accept': 'application/octet-stream',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        await for (final chunk in response.data!.stream
            .cast<List<int>>()
            .transform(utf8.decoder)
            .transform(LineSplitter())) {
          if (chunk.trim().isNotEmpty) {
            yield chunk;
          }
        }
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          error: 'Server returned status code: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout) {
        throw Exception('Connection timeout. Please check your network.');
      } else if (e.type == DioExceptionType.receiveTimeout) {
        throw Exception('Receive timeout. Server might be slow.');
      } else if (e.type == DioExceptionType.connectionError) {
        throw Exception('Connection error. Please check server availability.');
      } else {
        throw Exception('Network error: ${e.message}');
      }
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  void dispose() {
    _dio.close();
  }
}

class ChatProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final List<Message> _messages = [];
  bool _isStreaming = false;
  String? _currentStreamingMessageId;
  StreamSubscription? _streamSubscription;

  List<Message> get messages => List.unmodifiable(_messages);
  bool get isStreaming => _isStreaming;

  void addMessage(Message message) {
    _messages.add(message);
    notifyListeners();
  }

  void updateMessage(String messageId, String content,
      {MessageStatus? status}) {
    final index = _messages.indexWhere((msg) => msg.id == messageId);
    if (index != -1) {
      _messages[index] = _messages[index].copyWith(
        content: content,
        status: status,
      );
      notifyListeners();
    }
  }

  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty || _isStreaming) return;

    // Add user message
    final userMessage = Message(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content.trim(),
      type: MessageType.user,
      timestamp: DateTime.now(),
      status: MessageStatus.completed,
    );
    addMessage(userMessage);

    // Create assistant message placeholder
    final assistantMessageId =
        '${DateTime.now().millisecondsSinceEpoch}_assistant';
    final assistantMessage = Message(
      id: assistantMessageId,
      content: '',
      type: MessageType.assistant,
      timestamp: DateTime.now(),
      status: MessageStatus.streaming,
    );
    addMessage(assistantMessage);

    _isStreaming = true;
    _currentStreamingMessageId = assistantMessageId;
    notifyListeners();

    try {
      final stream = _apiService.sendStreamingMessage(content.trim());
      _streamSubscription = stream.listen(
        (chunk) {
          // Append chunk to the current assistant message
          final currentMessage = _messages.firstWhere(
            (msg) => msg.id == assistantMessageId,
          );
          final updatedContent = currentMessage.content + chunk;
          updateMessage(assistantMessageId, updatedContent);
        },
        onError: (error) {
          updateMessage(
            assistantMessageId,
            'Error: $error',
            status: MessageStatus.failed,
          );
          _finishStreaming();
        },
        onDone: () {
          updateMessage(
            assistantMessageId,
            _messages.firstWhere((msg) => msg.id == assistantMessageId).content,
            status: MessageStatus.completed,
          );
          _finishStreaming();
        },
      );
    } catch (error) {
      updateMessage(
        assistantMessageId,
        'Error: $error',
        status: MessageStatus.failed,
      );
      _finishStreaming();
    }
  }

  void _finishStreaming() {
    _isStreaming = false;
    _currentStreamingMessageId = null;
    _streamSubscription?.cancel();
    _streamSubscription = null;
    notifyListeners();
  }

  void stopStreaming() {
    if (_isStreaming && _currentStreamingMessageId != null) {
      _streamSubscription?.cancel();
      updateMessage(
        _currentStreamingMessageId!,
        _messages
            .firstWhere((msg) => msg.id == _currentStreamingMessageId!)
            .content,
        status: MessageStatus.completed,
      );
      _finishStreaming();
    }
  }

  void clearMessages() {
    _messages.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    _apiService.dispose();
    super.dispose();
  }
}

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Listen to messages changes to auto-scroll
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ChatProvider>().addListener(_scrollToBottom);
    });
  }

  @override
  void dispose() {
    context.read<ChatProvider>().removeListener(_scrollToBottom);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      Future.delayed(Duration(milliseconds: 100), () {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'Claude Streaming Chat',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[700],
        elevation: 0,
        actions: [
          Consumer<ChatProvider>(
            builder: (context, chatProvider, child) {
              return Row(
                children: [
                  if (chatProvider.isStreaming)
                    IconButton(
                      icon: Icon(Icons.stop_circle_outlined),
                      onPressed: chatProvider.stopStreaming,
                      tooltip: 'Stop streaming',
                    ),
                  IconButton(
                    icon: Icon(Icons.clear_all),
                    onPressed: chatProvider.messages.isNotEmpty
                        ? chatProvider.clearMessages
                        : null,
                    tooltip: 'Clear chat',
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Consumer<ChatProvider>(
              builder: (context, chatProvider, child) {
                if (chatProvider.messages.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.chat_bubble_outline,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        SizedBox(height: 16),
                        Text(
                          'Start a conversation with Claude',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Type a message below to begin',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  itemCount: chatProvider.messages.length,
                  itemBuilder: (context, index) {
                    final message = chatProvider.messages[index];
                    return MessageWidget(
                      message: message,
                      isStreaming: chatProvider.isStreaming &&
                          message.status == MessageStatus.streaming,
                    );
                  },
                );
              },
            ),
          ),
          InputWidget(),
        ],
      ),
    );
  }
}

class MessageWidget extends StatelessWidget {
  final Message message;
  final bool isStreaming;

  const MessageWidget({
    super.key,
    required this.message,
    this.isStreaming = false,
  });

  @override
  Widget build(BuildContext context) {
    final isUser = message.type == MessageType.user;
    final isError = message.type == MessageType.error ||
        message.status == MessageStatus.failed;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: isError ? Colors.red[100] : Colors.blue[100],
              child: Icon(
                isError ? Icons.error_outline : Icons.smart_toy,
                size: 18,
                color: isError ? Colors.red[700] : Colors.blue[700],
              ),
            ),
            SizedBox(width: 12),
          ],
          Expanded(
            child: Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isUser
                    ? Colors.blue[600]
                    : isError
                        ? Colors.red[50]
                        : Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: isError ? Border.all(color: Colors.red[200]!) : null,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: SelectableText(
                          message.content.isEmpty && isStreaming
                              ? 'Thinking...'
                              : message.content,
                          style: TextStyle(
                            color: isUser
                                ? Colors.white
                                : isError
                                    ? Colors.red[700]
                                    : Colors.grey[800],
                            fontSize: 16,
                            height: 1.4,
                          ),
                        ),
                      ),
                      if (!isUser && message.content.isNotEmpty)
                        IconButton(
                          icon: Icon(
                            Icons.copy_outlined,
                            size: 18,
                            color: Colors.grey[600],
                          ),
                          onPressed: () {
                            Clipboard.setData(
                                ClipboardData(text: message.content));
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Copied to clipboard'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          },
                          tooltip: 'Copy message',
                        ),
                    ],
                  ),
                  if (isStreaming) ...[
                    SizedBox(height: 8),
                    Row(
                      children: [
                        SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.blue[600]!,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          'Streaming...',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
          if (isUser) ...[
            SizedBox(width: 12),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey[200],
              child: Icon(
                Icons.person_outline,
                size: 18,
                color: Colors.grey[700],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class InputWidget extends StatefulWidget {
  const InputWidget({super.key});

  @override
  _InputWidgetState createState() => _InputWidgetState();
}

class _InputWidgetState extends State<InputWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Pre-fill with example message
    _controller.text = "Show me a real streaming response now";
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final content = _controller.text.trim();
    if (content.isNotEmpty) {
      context.read<ChatProvider>().sendMessage(content);
      _controller.clear();
      _focusNode.requestFocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatProvider>(
      builder: (context, chatProvider, child) {
        return Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(color: Colors.grey[200]!),
            ),
          ),
          child: SafeArea(
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    enabled: !chatProvider.isStreaming,
                    maxLines: 4,
                    minLines: 1,
                    decoration: InputDecoration(
                      hintText: chatProvider.isStreaming
                          ? 'Waiting for response...'
                          : 'Type your message...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(color: Colors.blue[600]!),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      suffixIcon: chatProvider.isStreaming
                          ? Padding(
                              padding: EdgeInsets.all(12),
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.blue[600]!,
                                  ),
                                ),
                              ),
                            )
                          : null,
                    ),
                    onSubmitted:
                        chatProvider.isStreaming ? null : (_) => _sendMessage(),
                  ),
                ),
                SizedBox(width: 12),
                FloatingActionButton(
                  onPressed: chatProvider.isStreaming ? null : _sendMessage,
                  backgroundColor: chatProvider.isStreaming
                      ? Colors.grey[300]
                      : Colors.blue[600],
                  mini: true,
                  child: Icon(
                    Icons.send,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
