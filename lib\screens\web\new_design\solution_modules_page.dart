import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:provider/provider.dart';

class SolutionModulesPage extends StatelessWidget {
  const SolutionModulesPage({super.key});
  @override
  Widget build(BuildContext context) {
    final SolutionModulesProvider provider = SolutionModulesProvider();
    return ChangeNotifierProvider.value(
      value: provider,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F7FA),
        body: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(child: SizedBox()),
            Expanded(
              flex: 8,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(context),
                  Expanded(
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 500),
                      padding:
                          const EdgeInsets.symmetric(vertical: AppSpacing.lg),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Container(
                              alignment: Alignment.topLeft,
                              constraints: const BoxConstraints(maxHeight: 600),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border.all(color: Color(0xffD0D0D0)),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Consumer<SolutionModulesProvider>(
                                builder: (context, provider, child) {
                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Header
                                      Container(
                                        height:
                                            provider.selectedSolutions.length >
                                                    3
                                                ? 60
                                                : 46,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            bottom: BorderSide(
                                                color: Color(0xffD0D0D0)),
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            // Add Solutions column header
                                            Expanded(
                                              child: Container(
                                                alignment: Alignment.centerLeft,
                                                padding: const EdgeInsets.only(
                                                    left: 16),
                                                decoration: BoxDecoration(
                                                  border: Border(
                                                    right: BorderSide(
                                                        color:
                                                            Color(0xffD0D0D0)),
                                                  ),
                                                ),
                                                child: Text(
                                                  'Add Solutions',
                                                  style: FontManager
                                                      .getCustomStyle(
                                                    fontSize: FontManager.s14,
                                                    fontWeight: FontWeight.w600,
                                                    color: Colors.black,
                                                  ),
                                                ),
                                              ),
                                            ), // Book Name column header
                                            Expanded(
                                              child: Container(
                                                alignment: Alignment.centerLeft,
                                                padding: const EdgeInsets.only(
                                                    left: 16),
                                                child: Text(
                                                  'Book Name',
                                                  style: FontManager
                                                      .getCustomStyle(
                                                    fontSize: FontManager.s14,
                                                    fontWeight: FontWeight.w600,
                                                    color: Colors.black,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      // Content
                                      Expanded(
                                        child: Row(
                                          children: [
                                            // Left column - Available Solutions
                                            Expanded(
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  border: Border(
                                                    right: BorderSide(
                                                        color:
                                                            Color(0xffD0D0D0)),
                                                  ),
                                                ),
                                                child: Column(
                                                  children: [
                                                    Expanded(
                                                      child: ListView.builder(
                                                        itemCount: provider
                                                            .availableSolutions
                                                            .length,
                                                        itemBuilder:
                                                            (context, index) {
                                                          final solution = provider
                                                                  .availableSolutions[
                                                              index];
                                                          return _buildSolutionItem(
                                                              solution,
                                                              () => provider
                                                                  .addSolutionToBook(
                                                                      solution));
                                                        },
                                                      ),
                                                    ),
                                                    // Pagination
                                                    _buildPagination(provider),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            // Right column - Selected Solutions
                                            Expanded(
                                              child: ListView.builder(
                                                itemCount: provider
                                                    .selectedSolutions.length,
                                                itemBuilder: (context, index) {
                                                  final solution = provider
                                                      .selectedSolutions[index];
                                                  return _buildSelectedSolutionItem(
                                                      solution);
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                          // Bottom buttons section
                          Container(
                            padding: const EdgeInsets.only(top: 16, bottom: 8),
                            decoration: const BoxDecoration(
                              color: Color(0xFFF5F7FA),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // Close button
                                MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () => Navigator.pop(context),
                                    child: Container(
                                      height: 36,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 24),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        border:
                                            Border.all(color: Color(0xFFD0D0D0)),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Center(
                                        child: const Text(
                                          'Close',
                                          style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                // Save button
                                MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () => Navigator.pop(context),
                                  child: Container(
                                    height: 36,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 24),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF0058FF),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      style: TextButton.styleFrom(
                                        padding: EdgeInsets.zero,
                                        minimumSize: Size.zero,
                                        tapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      ),
                                      child: Text(
                                        'Save',
                                        style: FontManager.getCustomStyle(
                                          color: Colors.white,
                                          fontSize: FontManager.s14,
                                          fontWeight: FontManager.medium,
                                        ),
                                      ),
                                    ),
                                  ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
            Expanded(child: SizedBox()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: AppSpacing.xxl,
      padding: const EdgeInsets.only(left: 0),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left section - Back button and title
          Row(
            children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: InkWell(
                  borderRadius: BorderRadius.circular(4),
                  onTap: () => Navigator.pop(context),
                  child: Icon(
                    Icons.arrow_back,
                    color: Colors.grey,
                    size: AppSpacing.md,
                  ),
                ),
              ),
              SizedBox(width: AppSpacing.xs),
              Text(
                'Add Solutions',
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.medium,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSolutionItem(String solution, VoidCallback onAdd) {
    return _HoverContainer(
      builder: (BuildContext context, bool isHovered) {
        return Container(
          constraints: const BoxConstraints(minHeight: 35),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          color: isHovered ? const Color(0xFFE6F0FF) : Colors.transparent,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  solution,
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                ),
              ),
              // Add button
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: onAdd,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: isHovered
                          ? const Color(0xFFD1E3FF)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: const Icon(
                      Icons.add,
                      size: 18,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSelectedSolutionItem(String solution) {
    return Consumer<SolutionModulesProvider>(
      builder: (context, provider, child) {
        return _HoverContainer(
          builder: (BuildContext context, bool isHovered) {
            return Container(
              constraints: const BoxConstraints(minHeight: 35),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              color: isHovered ? const Color(0xFFF0F0F0) : Colors.transparent,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      solution,
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontWeight.w400,
                        color: Colors.black,
                      ),
                    ),
                  ), // Remove button (cross icon that appears on hover)
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () => provider.removeSolutionFromBook(solution),
                      child: Opacity(
                        opacity: isHovered ? 1.0 : 0.0,
                        child: Container(
                          width: 20,
                          height: 20,
                          child: const Center(
                            child: Icon(
                              Icons.close,
                              size: 16,
                              color: Color(0xFF666666),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildPagination(SolutionModulesProvider provider) {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Left arrow
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: provider.canGoPrevious ? provider.previousPage : null,
              child: Icon(
                Icons.chevron_left,
                size: 24,
                color: provider.canGoPrevious ? Colors.black : Colors.grey,
              ),
            ),
          ),
          const SizedBox(width: 16),
          // Right arrow
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: provider.canGoNext ? provider.nextPage : null,
              child: Icon(
                Icons.chevron_right,
                size: 24,
                color: provider.canGoNext ? Colors.black : Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SolutionModulesProvider extends ChangeNotifier {
  int _currentPage = 1;
  final int _itemsPerPage = 12;

  // All potential solutions
  final List<String> _allPotentialSolutions = List.generate(
      50, (index) => 'Solutions-${(index + 1).toString().padLeft(2, '0')}');

  // Selected solutions that appear in the right column (Book Name)
  final List<String> _selectedSolutions = [];

  int get currentPage => _currentPage;
  List<String> get selectedSolutions => _selectedSolutions;

  // Filter out solutions that have already been added to the book
  List<String> get availableSolutions {
    // Get filtered list of solutions (excluding selected ones)
    List<String> filteredSolutions = _allPotentialSolutions
        .where((solution) => !_selectedSolutions.contains(solution))
        .toList();

    // Apply pagination
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;

    // Make sure we don't go out of bounds
    if (startIndex >= filteredSolutions.length) {
      return [];
    }

    return filteredSolutions.sublist(
      startIndex,
      endIndex > filteredSolutions.length ? filteredSolutions.length : endIndex,
    );
  }

  bool get canGoPrevious => _currentPage > 1;
  bool get canGoNext => _currentPage < totalPages;
  int get totalPages => (_allPotentialSolutions.length / _itemsPerPage).ceil();
  void addSolutionToBook(String solution) {
    if (!_selectedSolutions.contains(solution)) {
      _selectedSolutions.add(solution);
      notifyListeners();
    }
  }

  void removeSolutionFromBook(String solution) {
    _selectedSolutions.remove(solution);
    notifyListeners();
  }

  void previousPage() {
    if (canGoPrevious) {
      _currentPage--;
      notifyListeners();
    }
  }

  void nextPage() {
    if (canGoNext) {
      _currentPage++;
      notifyListeners();
    }
  }
}

class _HoverContainer extends StatefulWidget {
  final Widget Function(BuildContext context, bool isHovered) builder;

  const _HoverContainer({required this.builder});

  @override
  _HoverContainerState createState() => _HoverContainerState();
}

class _HoverContainerState extends State<_HoverContainer> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: widget.builder(context, isHovered),
    );
  }
}
